# Netlify Deployment Guide

This guide provides step-by-step instructions for deploying the NABERS Simulation Framework to Netlify.

## Prerequisites

1. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)
2. **GitHub Repository**: Your code should be pushed to a GitHub repository
3. **Gemini API Key**: Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

## Deployment Steps

### Method 1: Connect GitHub Repository (Recommended)

1. **Log in to Netlify** and go to your dashboard
2. **Click "New site from Git"**
3. **Choose GitHub** as your Git provider
4. **Select your repository** (`nabers-sim-framework`)
5. **Configure build settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Node version: `18.x`
   - Production branch: `main` (or `master` depending on your default branch)
6. **Configure branch deploys** (optional):
   - Enable branch deploys for feature branches
   - Enable deploy previews for pull requests

### Method 2: Drag and Drop Deployment

1. **Build the project locally**:
   ```bash
   npm install
   npm run build
   ```
2. **Drag the `dist` folder** to Netlify's deployment area

## Environment Variables Configuration

After deployment, you MUST configure environment variables:

1. **Go to Site settings** > **Environment variables**
2. **Add the following variables**:
   - `GEMINI_API_KEY`: Your Google Gemini API key
   - `NODE_ENV`: `production`

### Getting Your Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key
5. Add it to Netlify environment variables

## Build Configuration

The project includes `netlify.toml` with optimized settings:

- **Build command**: `npm run build`
- **Publish directory**: `dist`
- **Node version**: 18.x
- **Production branch**: `main` (configurable)
- **SPA redirects**: Configured for React Router
- **Security headers**: Added for better security
- **Asset caching**: Optimized for performance

### Branch Deployment Strategy

- **Production**: Deploys from `main` branch to your primary domain
- **Deploy Previews**: Automatic previews for pull requests
- **Branch Deploys**: Optional deploys for feature branches

To configure branch deploys:
1. Go to **Site settings** > **Build & deploy** > **Branch deploys**
2. Choose **Deploy only the production branch** (recommended) or **Deploy all branches**
3. Enable **Deploy previews** for pull requests

## Custom Domain (Optional)

1. **Go to Site settings** > **Domain management**
2. **Add custom domain** if you have one
3. **Configure DNS** according to Netlify's instructions
4. **Enable HTTPS** (automatic with Netlify)

## Environment-Specific Features

### Development
- Local development: `npm run dev`
- Preview builds: `npm run preview`
- Netlify dev: `netlify dev` (if Netlify CLI installed)

### Production
- Automatic builds on Git push
- Branch previews for pull requests
- Form handling (if forms are added)
- Function support (for future serverless functions)

## Troubleshooting

### Common Issues

1. **Build Fails**:
   - Check Node.js version (should be 18.x)
   - Verify all dependencies are installed
   - Check build logs in Netlify dashboard

2. **API Key Not Working**:
   - Verify `GEMINI_API_KEY` is set in environment variables
   - Check API key format and permissions
   - Ensure the key is active in Google AI Studio

3. **404 Errors on Routes**:
   - Verify `_redirects` file is in `public` folder
   - Check `netlify.toml` redirect configuration

4. **TypeScript Errors**:
   - Ensure all `@types/*` packages are installed
   - Check `tsconfig.json` configuration

### Build Logs

Monitor build progress and errors:
1. **Go to Deploys** tab in Netlify dashboard
2. **Click on a deploy** to see detailed logs
3. **Check for errors** in the build output

## Performance Optimization

The deployment includes several optimizations:

- **Static asset caching** (1 year for `/assets/*`)
- **Security headers** for better security score
- **Gzip compression** (automatic)
- **CDN distribution** (global)

## Monitoring and Analytics

Consider adding:
- **Netlify Analytics** for traffic insights
- **Google Analytics** for user behavior
- **Performance monitoring** tools

## Security Considerations

- API keys are securely stored as environment variables
- Security headers are configured in `netlify.toml`
- HTTPS is enforced by default
- Sensitive paths are blocked in `robots.txt`

## Continuous Deployment

Once connected to GitHub:
- **Automatic deploys** on push to production branch (`main`)
- **Deploy previews** for pull requests (optional)
- **Branch deploys** for feature branches (optional)
- **Rollback capability** to previous versions

### Branch Configuration

The `netlify.toml` file includes configurations for:
- **Production context**: Main branch deployment
- **Deploy-preview context**: Pull request previews
- **Branch-deploy context**: Feature branch deployment

### Deployment URLs

- **Production**: `https://your-site-name.netlify.app`
- **Deploy previews**: `https://deploy-preview-[PR#]--your-site-name.netlify.app`
- **Branch deploys**: `https://[branch-name]--your-site-name.netlify.app`

## CLI Deployment (Advanced)

Install Netlify CLI for advanced features:
```bash
npm install -g netlify-cli
netlify login
netlify deploy --prod
```

## Support

- **Netlify Documentation**: [docs.netlify.com](https://docs.netlify.com)
- **Community Support**: [community.netlify.com](https://community.netlify.com)
- **Status Page**: [status.netlify.com](https://status.netlify.com)