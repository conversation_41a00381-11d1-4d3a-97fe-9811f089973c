{"name": "nabers-framework-simulation-web-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.16.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "recharts": "^3.1.2"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "~5.8.2", "vite": "^6.2.0"}}