export enum BuildingType {
  Office = 'Office Block',
  Hotel = 'Hotel',
  ShoppingCentre = 'Shopping Centre',
  Hospital = 'Hospital',
}

export enum AudienceMode {
  Consultant = 'NABERS Consultant',
  Owner = 'Building Owner',
  FM = 'HVAC FM',
}

export enum Theme {
  Day = 'Daylight',
  Grey = 'Grey',
  Night = 'Night',
}

export enum OccupantDensity {
    Low = 'Low',
    Typical = 'Typical',
    High = 'High',
}

export enum ChatbotMode {
  Explanation = 'Model Explanation',
  ParameterSetting = 'Set Model Parameters',
}

export enum PaybackStatus {
  Profitable = 'profitable',     // < 10 years
  LongTerm = 'long-term',        // 10-25 years  
  Unprofitable = 'unprofitable'  // Never pays back
}

export interface NabersRatings {
  energy: number;
  water: number;
  waste: number;
  ieq: number;
  carbon: number;
}

export interface SimulationParams {
  buildingType: BuildingType;
  aiEffectiveness: number; // 0-100%
  iterations: number;
  // Financial Parameters
  buildingArea: number; // m^2
  energyCost: number; // $/kWh
  hvacEnergyShare: number; // 0-100%
  // AI System Financial Parameters
  aiSystemCapex: number; // One-time capital expenditure ($)
  aiSystemOpex: number; // Annual operational expenditure ($/year)
  // IEQ Parameters
  occupantDensity: OccupantDensity;
  temperatureBand: [number, number];
  humidityBand: [number, number];
  freshAirIntake: number; // L/s/person
}

export interface SimulationResult {
  energySavingsDistribution: number[];
  probabilityMeetingTarget: number;
  expectedStarRating: number;
  avgEnergySaving: number;
  // New IEQ Results
  ieqScore: number;
  ieqEnergyOverheadPercent: number;
  complaintReduction: number;
}

export interface FinancialImpact {
  costSavings: number;
  carbonSavings: number;
  paybackPeriod: number | null; // null when never pays back
  paybackStatus: PaybackStatus;
}

export interface Scenario {
  name: string;
  params: SimulationParams;
  result: SimulationResult;
  financialImpact: FinancialImpact;
}

export interface ChatMessage {
    role: 'user' | 'model';
    parts: { text: string }[];
}