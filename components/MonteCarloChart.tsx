
import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer, ReferenceLine, Cell } from 'recharts';
import { type SimulationResult } from '../types';
import { Icon } from './Icon';

interface MonteCarloChartProps {
  result: SimulationResult;
  onExplain: () => void;
}

const MonteCarloChart: React.FC<MonteCarloChartProps> = ({ result, onExplain }) => {
  const stats = useMemo(() => {
    const values = result.energySavingsDistribution;
    const min = Math.min(...values);
    const max = Math.max(...values);
    const binCount = 30;
    const binWidth = (max - min) / binCount;

    const bins = Array.from({ length: binCount }, (_, i) => {
      const start = min + i * binWidth;
      const center = start + binWidth / 2;
      return { name: start.toFixed(1), frequency: 0, center };
    });

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    let closestBinName = bins[0].name;
    let minDistance = Math.abs(bins[0].center - avg);
    for (let i = 1; i < bins.length; i++) {
      const distance = Math.abs(bins[i].center - avg);
      if (distance < minDistance) {
        minDistance = distance;
        closestBinName = bins[i].name;
      }
    }

    return { values, bins, avg, stdDev, closestBinName, min, binWidth, binCount };
  }, [result]);

  const [bins, setBins] = useState(() => stats.bins.map(b => ({ ...b, withinStdDev: false })));
  const [iterationsProcessed, setIterationsProcessed] = useState(0);

  useEffect(() => {
    const { values, bins: baseBins, avg, stdDev, min, binWidth, binCount } = stats;
    const initialBins = baseBins.map(b => ({
      name: b.name,
      frequency: 0,
      withinStdDev: Math.abs(b.center - avg) <= stdDev,
    }));
    setBins(initialBins);
    setIterationsProcessed(0);

    let processed = 0;
    const batchSize = 100; // iterations per frame
    const binsCopy = initialBins.map(b => ({ ...b }));

    const step = () => {
      const next = Math.min(processed + batchSize, values.length);
      for (let i = processed; i < next; i++) {
        const value = values[i];
        const binIndex = Math.min(binCount - 1, Math.floor((value - min) / binWidth));
        binsCopy[binIndex].frequency++;
      }
      processed = next;
      setBins(binsCopy.map(b => ({ ...b })));
      setIterationsProcessed(processed);
      if (processed < values.length) {
        requestAnimationFrame(step);
      }
    };

    requestAnimationFrame(step);
  }, [stats]);

  const avgEnergySaving = stats.avg;
  
  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  const accentColor = "text-day-accent dark:text-night-accent grey-theme:text-grey-accent";
  
  return (
    <div className={`p-4 rounded-lg shadow-lg border h-full flex flex-col ${themeCardClass} ${themeBorderClass}`}>
       <div className="flex items-center justify-between">
        <h3 className="text-lg font-bold mb-2 flex items-center">
            <Icon name="chart" className="h-6 w-6 mr-2" />
            Monte Carlo Simulation
        </h3>
        <button 
            onClick={onExplain} 
            className={`p-1 rounded-full ${accentColor} hover:bg-black/10 dark:hover:bg-white/10 transition-colors`} 
            title="Ask AI to Explain"
            aria-label="Ask AI to Explain Monte Carlo Chart"
        >
            <Icon name="sparkles" className="h-5 w-5" />
       </button>
       </div>
       <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
        Likelihood of Meeting NABERS Energy Target: <span className={`font-bold ${accentColor}`}>{(result.probabilityMeetingTarget * 100).toFixed(1)}%</span>
        <span className="ml-4">Iterations: {iterationsProcessed}/{result.energySavingsDistribution.length}</span>
      </p>
      <div className="flex-grow">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={bins} margin={{ top: 5, right: 20, left: -10, bottom: 45 }}>
            <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2} />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              label={{
                value: 'Energy Savings (kWh/m²/year)', 
                position: 'insideBottom', 
                offset: -5,
                style: { textAnchor: 'middle', fontSize: '12px' }
              }}
            />
            <YAxis 
              tick={{ fontSize: 12 }} 
              label={{ 
                value: 'Frequency', 
                angle: -90, 
                position: 'insideLeft',
                style: { textAnchor: 'middle', fontSize: '12px' }
              }}
            />
            <Tooltip
                cursor={{ fill: 'rgba(128, 128, 128, 0.1)' }}
                labelFormatter={(value) => `${value} kWh/m²/year`}
                formatter={(value: number) => [value, 'Frequency']}
            />
            <ReferenceLine
              x={stats.closestBinName}
              stroke="#3b82f6"
              strokeDasharray="3 3"
              strokeWidth={2}
              label={{
                value: `Avg: ${avgEnergySaving.toFixed(1)}`,
                position: 'insideTopRight',
                fill: '#3b82f6',
                fontSize: '11px'
              }}
            />
            <Bar dataKey="frequency" name="Frequency">
              {bins.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.withinStdDev ? 'url(#colorHighlight)' : 'url(#colorUv)'} />
              ))}
            </Bar>
            <Legend
              wrapperStyle={{ fontSize: "14px", paddingTop: "10px" }}
              verticalAlign="bottom"
              height={36}
            />

            <defs>
              <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#8884d8" stopOpacity={0.2} />
              </linearGradient>
              <linearGradient id="colorHighlight" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.2} />
              </linearGradient>
            </defs>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default MonteCarloChart;
