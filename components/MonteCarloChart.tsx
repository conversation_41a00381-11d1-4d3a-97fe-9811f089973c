
import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, ReferenceLine, Cell } from 'recharts';
import { type SimulationResult } from '../types';
import { Icon } from './Icon';

interface MonteCarloChartProps {
  result: SimulationResult;
  onExplain: () => void;
}

const MonteCarloChart: React.FC<MonteCarloChartProps> = ({ result, onExplain }) => {
  // Calculate both gross and net averages for display
  const grossAverage = result.energySavingsDistribution.reduce((sum, val) => sum + val, 0) / result.energySavingsDistribution.length;
  const netAverage = result.avgEnergySaving; // Already adjusted for IEQ overhead
  const stats = useMemo(() => {
    const values = result.energySavingsDistribution;
    const binCount = 30;

    // Single-pass calculation for min, max, sum, and variance
    let min = values[0];
    let max = values[0];
    let sum = 0;
    let sumSquares = 0;

    for (let i = 0; i < values.length; i++) {
      const val = values[i];
      if (val < min) min = val;
      if (val > max) max = val;
      sum += val;
      sumSquares += val * val;
    }

    const avg = sum / values.length;
    const variance = (sumSquares / values.length) - (avg * avg);
    const stdDev = Math.sqrt(Math.max(0, variance)); // Ensure non-negative for numerical stability

    // Handle edge case where all values are the same
    const range = max - min;
    const binWidth = range > 0 ? range / binCount : 1;

    const bins = Array.from({ length: binCount }, (_, i) => {
      const start = min + i * binWidth;
      const center = start + binWidth / 2;
      return { name: start.toFixed(1), frequency: 0, center };
    });

    // Find closest bin to average using direct calculation instead of loop
    const avgBinIndex = range > 0 ? Math.min(binCount - 1, Math.floor((avg - min) / binWidth)) : 0;
    const closestBinName = bins[avgBinIndex].name;

    // Calculate likelihood (probability of meeting target)
    const likelihood = result.probabilityMeetingTarget * 100;

    // Find closest bin to net average for second reference line
    const netBinIndex = range > 0 ? Math.min(binCount - 1, Math.floor((netAverage - min) / binWidth)) : 0;
    const netClosestBinName = bins[netBinIndex].name;

    return { values, bins, avg, stdDev, closestBinName, netClosestBinName, min, binWidth, binCount, likelihood };
  }, [result, netAverage]);

  const processedBins = useMemo(() => {
    const { values, bins: baseBins, avg, stdDev, min, binWidth, binCount } = stats;

    // Initialize bins with zero frequency
    const workingBins = baseBins.map(b => ({
      name: b.name,
      frequency: 0,
      center: b.center,
      withinStdDev: false
    }));

    // Process all values at once - no animation
    for (let i = 0; i < values.length; i++) {
      const value = values[i];
      if (binWidth > 0) {
        const binIndex = Math.min(binCount - 1, Math.max(0, Math.floor((value - min) / binWidth)));
        workingBins[binIndex].frequency++;
      } else {
        // All values are the same, put everything in the first bin
        workingBins[0].frequency++;
      }
    }

    // After processing all values, determine which bins are within 1 standard deviation
    workingBins.forEach(bin => {
      bin.withinStdDev = Math.abs(bin.center - avg) <= stdDev;
    });

    return workingBins;
  }, [stats]);

  const avgEnergySaving = stats.avg;
  
  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  const accentColor = "text-day-accent dark:text-night-accent grey-theme:text-grey-accent";
  
  return (
    <div className={`p-4 rounded-lg shadow-lg border h-full flex flex-col ${themeCardClass} ${themeBorderClass}`}>
       <div className="flex items-center justify-between">
        <h3 className="text-lg font-bold mb-2 flex items-center">
            <Icon name="chart" className="h-6 w-6 mr-2" />
            Monte Carlo Simulation

        </h3>
        <button 
            onClick={onExplain} 
            className={`p-1 rounded-full ${accentColor} hover:bg-black/10 dark:hover:bg-white/10 transition-colors`} 
            title="Ask AI to Explain"
            aria-label="Ask AI to Explain Monte Carlo Chart"
        >
            <Icon name="sparkles" className="h-5 w-5" />
       </button>
       </div>
       <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
        Likelihood of Meeting NABERS Energy Target: <span className={`font-bold ${accentColor}`}>{stats.likelihood.toFixed(1)}%</span>
        <span className="ml-4">
          Iterations: {stats.values.length.toLocaleString()}
        </span>
      </p>
      <p className="text-xs text-gray-400 dark:text-gray-500 mb-4">
        <span className="text-blue-500">■</span> Gross Savings: {grossAverage.toFixed(1)} kWh/m²/year
        <span className="ml-4 text-green-500">■</span> Net Savings: {netAverage.toFixed(1)} kWh/m²/year (after IEQ overhead)
      </p>
      <div className="flex-grow">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={processedBins} margin={{ top: 5, right: 20, left: -10, bottom: 45 }}>
            <defs>
              <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#8884d8" stopOpacity={0.2} />
              </linearGradient>
              <linearGradient id="colorHighlight" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.2} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.2} />
            <XAxis
              dataKey="name"
              tick={{ fontSize: 12 }}
              label={{
                value: 'Energy Savings (kWh/m²/year)',
                position: 'insideBottom',
                offset: -5,
                style: { textAnchor: 'middle', fontSize: '12px' }
              }}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              label={{
                value: 'Frequency',
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle', fontSize: '12px' }
              }}
            />
            <Tooltip
                cursor={{ fill: 'rgba(128, 128, 128, 0.1)' }}
                labelFormatter={(value) => `${value} kWh/m²/year`}
                formatter={(value: number) => [value, 'Frequency']}
            />
            <ReferenceLine
              x={stats.closestBinName}
              stroke="#3b82f6"
              strokeDasharray="3 3"
              strokeWidth={2}
              label={{
                value: `Gross: ${grossAverage.toFixed(1)}`,
                position: 'insideTopRight',
                fill: '#3b82f6',
                fontSize: '11px'
              }}
            />
            <ReferenceLine
              x={stats.netClosestBinName}
              stroke="#10b981"
              strokeDasharray="5 5"
              strokeWidth={2}
              label={{
                value: `Net: ${netAverage.toFixed(1)}`,
                position: 'insideTopLeft',
                fill: '#10b981',
                fontSize: '11px'
              }}
            />
            <Bar dataKey="frequency" name="Frequency">
              {processedBins.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.withinStdDev ? 'url(#colorHighlight)' : 'url(#colorUv)'} />
              ))}
            </Bar>
            <Legend
              wrapperStyle={{ fontSize: "14px", paddingTop: "10px" }}
              verticalAlign="bottom"
              height={36}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default MonteCarloChart;
