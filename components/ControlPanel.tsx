import React from 'react';
import { type SimulationParams, BuildingType, OccupantDensity } from '../types';
import { Icon } from './Icon';

interface ControlPanelProps {
  params: SimulationParams;
  setParams: React.Dispatch<React.SetStateAction<SimulationParams>>;
  onRunSimulation: () => void;
  isLoading: boolean;
  buildingTypes: BuildingType[];
}

const ControlPanel: React.FC<ControlPanelProps> = ({ params, setParams, onRunSimulation, isLoading, buildingTypes }) => {
    const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
    const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
    const themeAccentClass = "accent-day-accent dark:accent-night-accent grey-theme:accent-grey-accent";
    const themeButtonClass = "bg-day-accent hover:bg-day-accent-hover dark:bg-night-accent dark:hover:bg-night-accent-hover grey-theme:bg-grey-accent grey-theme:hover:bg-grey-accent-hover";
    const themeInputClass = "bg-day-bg dark:bg-night-bg grey-theme:bg-grey-bg border";

  return (
    <div className={`p-6 rounded-lg shadow-lg border ${themeCardClass} ${themeBorderClass} animate-fadeIn`}>
      <h2 className="text-xl font-bold mb-6 flex items-center">
        <Icon name="settings" className="h-6 w-6 mr-2" />
        Simulation Controls
      </h2>

      <div className="space-y-6">
        {/* Core Controls */}
        <div className="border-b pb-6 mb-6 border-day-border dark:border-night-border grey-theme:border-grey-border">
            <h3 className="text-lg font-semibold mb-4">Core Parameters</h3>
            <div>
              <label htmlFor="building-type" className="block text-sm font-medium mb-2">
                Building Type
              </label>
              <select
                id="building-type"
                value={params.buildingType}
                onChange={(e) => setParams({ ...params, buildingType: e.target.value as BuildingType })}
                className={`w-full p-2 rounded-md border ${themeBorderClass} ${themeInputClass} focus:ring-2 focus:ring-opacity-50`}
              >
                {buildingTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div className="mt-4">
              <label htmlFor="ai-effectiveness" className="block text-sm font-medium mb-2">
                AI Control Effectiveness: <span className="font-semibold">{params.aiEffectiveness}%</span>
              </label>
              <input
                id="ai-effectiveness"
                type="range"
                min="10"
                max="100"
                step="5"
                value={params.aiEffectiveness}
                onChange={(e) => setParams({ ...params, aiEffectiveness: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
            </div>
            
            <div className="mt-4">
              <label htmlFor="building-area" className="block text-sm font-medium mb-2">
                Building Area: <span className="font-semibold">{params.buildingArea.toLocaleString()} m²</span>
              </label>
              <input
                id="building-area"
                type="range"
                min="1000"
                max="100000"
                step="1000"
                value={params.buildingArea}
                onChange={(e) => setParams({ ...params, buildingArea: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
            </div>

            <div className="mt-4">
              <label htmlFor="energy-cost" className="block text-sm font-medium mb-2">
                Energy Cost: <span className="font-semibold">${params.energyCost.toFixed(2)} / kWh</span>
              </label>
              <input
                id="energy-cost"
                type="range"
                min="0.05"
                max="0.50"
                step="0.01"
                value={params.energyCost}
                onChange={(e) => setParams({ ...params, energyCost: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
            </div>

             <div className="mt-4">
              <label htmlFor="hvac-share" className="block text-sm font-medium mb-2">
                HVAC Energy Share: <span className="font-semibold">{params.hvacEnergyShare}%</span>
              </label>
              <input
                id="hvac-share"
                type="range"
                min="20"
                max="80"
                step="1"
                value={params.hvacEnergyShare}
                onChange={(e) => setParams({ ...params, hvacEnergyShare: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
            </div>

            <div className="mt-4">
              <label htmlFor="ai-capex" className="block text-sm font-medium mb-2">
                AI System CAPEX: <span className="font-semibold">${(params.aiSystemCapex / 1000).toFixed(0)}k</span>
              </label>
              <input
                id="ai-capex"
                type="range"
                min="50000"
                max="500000"
                step="10000"
                value={params.aiSystemCapex}
                onChange={(e) => setParams({ ...params, aiSystemCapex: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>$50k</span>
                <span>$500k</span>
              </div>
            </div>

            <div className="mt-4">
              <label htmlFor="ai-opex" className="block text-sm font-medium mb-2">
                AI System OPEX: <span className="font-semibold">${(params.aiSystemOpex / 1000).toFixed(0)}k/year</span>
              </label>
              <input
                id="ai-opex"
                type="range"
                min="5000"
                max="50000"
                step="1000"
                value={params.aiSystemOpex}
                onChange={(e) => setParams({ ...params, aiSystemOpex: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>$5k</span>
                <span>$50k</span>
              </div>
            </div>
        </div>

        {/* IEQ Controls */}
        <div>
            <h3 className="text-lg font-semibold mb-4">IEQ Parameters</h3>
            <div>
                <label htmlFor="occupant-density" className="block text-sm font-medium mb-2">Occupant Density</label>
                <select
                    id="occupant-density"
                    value={params.occupantDensity}
                    onChange={(e) => setParams({ ...params, occupantDensity: e.target.value as OccupantDensity })}
                    className={`w-full p-2 rounded-md border ${themeBorderClass} ${themeInputClass}`}
                >
                    {Object.values(OccupantDensity).map(density => <option key={density} value={density}>{density}</option>)}
                </select>
            </div>
            <div className="mt-4">
                <label className="block text-sm font-medium mb-2">Desired Temperature Band: <span className="font-semibold">{params.temperatureBand[0].toFixed(1)}°C - {params.temperatureBand[1].toFixed(1)}°C</span></label>
                <div className="flex items-center space-x-2">
                    <input type="range" min="19" max="23" step="0.5" value={params.temperatureBand[0]} onChange={(e) => setParams(p => ({ ...p, temperatureBand: [Number(e.target.value), Math.max(Number(e.target.value) + 1, p.temperatureBand[1])] }))} className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`} />
                    <input type="range" min="20" max="26" step="0.5" value={params.temperatureBand[1]} onChange={(e) => setParams(p => ({ ...p, temperatureBand: [Math.min(Number(e.target.value) - 1, p.temperatureBand[0]), Number(e.target.value)] }))} className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`} />
                </div>
            </div>
            <div className="mt-4">
                <label className="block text-sm font-medium mb-2">Desired Humidity Band: <span className="font-semibold">{params.humidityBand[0]}% - {params.humidityBand[1]}% RH</span></label>
                 <div className="flex items-center space-x-2">
                    <input type="range" min="30" max="55" step="1" value={params.humidityBand[0]} onChange={(e) => setParams(p => ({ ...p, humidityBand: [Number(e.target.value), Math.max(Number(e.target.value) + 5, p.humidityBand[1])] }))} className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`} />
                    <input type="range" min="35" max="60" step="1" value={params.humidityBand[1]} onChange={(e) => setParams(p => ({ ...p, humidityBand: [Math.min(Number(e.target.value) - 5, p.humidityBand[0]), Number(e.target.value)] }))} className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`} />
                </div>
            </div>
            <div className="mt-4">
              <label htmlFor="fresh-air-intake" className="block text-sm font-medium mb-2">
                Min Fresh Air Intake: <span className="font-semibold">{params.freshAirIntake} L/s/person</span>
              </label>
              <input
                id="fresh-air-intake"
                type="range"
                min="5"
                max="20"
                step="1"
                value={params.freshAirIntake}
                onChange={(e) => setParams({ ...params, freshAirIntake: Number(e.target.value) })}
                className={`w-full h-2 rounded-lg appearance-none cursor-pointer ${themeAccentClass}`}
              />
            </div>
        </div>

      </div>

      <button
        onClick={onRunSimulation}
        disabled={isLoading}
        className={`w-full mt-8 py-3 px-4 text-white font-semibold rounded-lg shadow-md transition-transform transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${themeButtonClass}`}
      >
        {isLoading ? (
            <div className="flex items-center justify-center">
                <Icon name="loader" className="animate-spin h-5 w-5 mr-3" />
                Simulating...
            </div>
        ) : (
             <div className="flex items-center justify-center">
                <Icon name="play" className="h-5 w-5 mr-2" />
                Run Simulation
            </div>
        )}
      </button>
    </div>
  );
};

export default ControlPanel;