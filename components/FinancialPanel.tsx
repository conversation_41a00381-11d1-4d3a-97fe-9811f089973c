
import React from 'react';
import { type FinancialImpact, AudienceMode, PaybackStatus } from '../types';
import { Icon } from './Icon';

interface FinancialPanelProps {
  financialImpact: FinancialImpact;
  audienceMode: AudienceMode;
}

const FinancialPanel: React.FC<FinancialPanelProps> = ({ financialImpact, audienceMode }) => {
    const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
    const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";

  const renderPaybackPeriod = () => {
    const { paybackPeriod, paybackStatus } = financialImpact;
    
    switch (paybackStatus) {
      case PaybackStatus.Profitable:
        return (
          <div className="flex items-start p-4 rounded-lg bg-green-500/10">
            <Icon name="graph" className="h-8 w-8 text-green-400 mt-1 mr-4 flex-shrink-0" />
            <div>
              <p className="text-sm text-green-300">Payback Period</p>
              <p className="text-3xl font-bold text-green-400">
                {paybackPeriod!.toFixed(1)} <span className="text-lg">years</span>
              </p>
              <p className="text-xs text-green-300 mt-1">Excellent investment</p>
            </div>
          </div>
        );
        
      case PaybackStatus.LongTerm:
        return (
          <div className="flex items-start p-4 rounded-lg bg-yellow-500/10">
            <Icon name="graph" className="h-8 w-8 text-yellow-400 mt-1 mr-4 flex-shrink-0" />
            <div>
              <p className="text-sm text-yellow-300">Payback Period</p>
              <p className="text-3xl font-bold text-yellow-400">
                {paybackPeriod!.toFixed(1)} <span className="text-lg">years</span>
              </p>
              <p className="text-xs text-yellow-300 mt-1">Long-term investment</p>
            </div>
          </div>
        );
        
      case PaybackStatus.Unprofitable:
        return (
          <div className="flex items-start p-4 rounded-lg bg-red-500/10">
            <Icon name="info" className="h-8 w-8 text-red-400 mt-1 mr-4 flex-shrink-0" />
            <div>
              <p className="text-sm text-red-300">Investment Analysis</p>
              <p className="text-lg font-bold text-red-400">Never pays back</p>
              <p className="text-xs text-red-300 mt-1">OPEX exceeds savings</p>
            </div>
          </div>
        );
        
      default:
        return (
          <div className="flex items-start p-4 rounded-lg bg-gray-500/10">
            <Icon name="graph" className="h-8 w-8 text-gray-400 mt-1 mr-4 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-300">Payback Period</p>
              <p className="text-lg font-bold text-gray-400">Calculating...</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`p-6 rounded-lg shadow-lg border h-full ${themeCardClass} ${themeBorderClass}`}>
      <h3 className="text-lg font-bold mb-4 flex items-center">
        <Icon name="cash" className="h-6 w-6 mr-2" />
        Financial & Carbon Impact
      </h3>
      <div className="space-y-6">
        <div className="flex items-start p-4 rounded-lg bg-green-500/10">
          <Icon name="dollar" className="h-8 w-8 text-green-400 mt-1 mr-4 flex-shrink-0" />
          <div>
            <p className="text-sm text-green-300">Est. Annual Cost Savings</p>
            <p className="text-3xl font-bold text-green-400">
              ${financialImpact.costSavings.toLocaleString('en-US', { maximumFractionDigits: 0 })}
            </p>
          </div>
        </div>
        
        <div className="flex items-start p-4 rounded-lg bg-sky-500/10">
          <Icon name="leaf" className="h-8 w-8 text-sky-400 mt-1 mr-4 flex-shrink-0" />
          <div>
            <p className="text-sm text-sky-300">Est. Annual Carbon Savings</p>
            <p className="text-3xl font-bold text-sky-400">
              {financialImpact.carbonSavings.toLocaleString('en-US', { maximumFractionDigits: 1 })}
              <span className="text-lg ml-1">tCO₂-e</span>
            </p>
          </div>
        </div>

        {(audienceMode === AudienceMode.Owner || audienceMode === AudienceMode.Consultant) && (
          renderPaybackPeriod()
        )}
      </div>
    </div>
  );
};

export default FinancialPanel;