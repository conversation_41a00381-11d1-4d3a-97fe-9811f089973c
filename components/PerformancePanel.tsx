
import React from 'react';
import { type SimulationParams, type SimulationResult } from '../types';
import { BASELINE_NABERS, TARGET_NABERS } from '../constants';
import { Icon } from './Icon';

interface PerformancePanelProps {
  params: SimulationParams;
  result: SimulationResult | null;
  isLoading: boolean;
  onExplainIEQ: () => void;
}

const PerformanceItem: React.FC<{
  icon: React.ComponentProps<typeof Icon>['name'];
  title: string;
  baseline: number;
  target: number;
  simulated: number;
  isLoading: boolean;
  color: string;
}> = ({ icon, title, baseline, target, simulated, isLoading, color }) => {
  const maxStars = 6;
  const simulatedClamped = Math.min(simulated, maxStars);
  const progress = (simulatedClamped / maxStars) * 100;
  
  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  
  return (
    <div className={`p-4 rounded-lg border flex flex-col justify-between ${themeCardClass} ${themeBorderClass}`}>
      <div>
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold">{title}</h4>
          <Icon name={icon} className={`h-6 w-6 ${color}`} />
        </div>
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
          <span>Baseline: {baseline.toFixed(1)}</span>
          <span>Target: {target.toFixed(1)}</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2">
          <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: `${(baseline/maxStars)*100}%` }}></div>
          <div className={`${color.replace('text-', 'bg-')} h-2.5 rounded-full -mt-2.5`} style={{ width: `${progress}%` }}></div>
        </div>
      </div>
      <div className="text-center mt-2">
        {isLoading ? (
          <div className="h-8 w-16 mx-auto bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
        ) : (
          <p className="text-2xl font-bold">
            <span className={color}>{simulated.toFixed(1)}</span>
            <span className="text-lg font-normal text-gray-500 dark:text-gray-400">/6</span>
          </p>
        )}
        <p className="text-sm">Simulated Stars</p>
      </div>
    </div>
  );
};

const IEQPerformanceItem: React.FC<{
    result: SimulationResult | null;
    isLoading: boolean;
    onExplain: () => void;
}> = ({ result, isLoading, onExplain }) => {
    const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
    const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
    const accentColor = "text-day-accent dark:text-night-accent grey-theme:text-grey-accent";
    const color = "text-purple-400";

    const LoadingPill = () => <div className="h-5 w-20 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>;

    return (
        <div className={`p-4 rounded-lg border flex flex-col justify-between ${themeCardClass} ${themeBorderClass}`}>
            <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                    <h4 className="font-semibold">Indoor Env.</h4>
                    <button 
                        onClick={onExplain} 
                        className={`p-1 rounded-full ${accentColor} hover:bg-black/10 dark:hover:bg-white/10 transition-colors`} 
                        title="Ask AI to Explain"
                        aria-label="Ask AI to Explain IEQ Results"
                    >
                        <Icon name="sparkles" className="h-5 w-5" />
                    </button>
                </div>
                <Icon name="air" className={`h-6 w-6 ${color}`} />
            </div>
            <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                    <span className="text-gray-500 dark:text-gray-400">IEQ Score:</span>
                    {isLoading || !result ? <LoadingPill /> : <span className={`font-bold text-lg ${color}`}>{result.ieqScore.toFixed(0)}<span className="text-xs">/100</span></span>}
                </div>
                <div className="flex justify-between items-center">
                    <span className="text-gray-500 dark:text-gray-400">Energy Overhead:</span>
                    {isLoading || !result ? <LoadingPill /> : <span className="font-bold">{result.ieqEnergyOverheadPercent.toFixed(1)}%</span>}
                </div>
                <div className="flex justify-between items-center">
                    <span className="text-gray-500 dark:text-gray-400">Complaint Reduction:</span>
                    {isLoading || !result ? <LoadingPill /> : <span className="font-bold text-green-400">{result.complaintReduction.toFixed(0)}%</span>}
                </div>
            </div>
        </div>
    );
};


const PerformancePanel: React.FC<PerformancePanelProps> = ({ params, result, isLoading, onExplainIEQ }) => {
  const baseline = BASELINE_NABERS[params.buildingType];
  const target = TARGET_NABERS[params.buildingType];
  
  const simulatedRatings = {
      energy: result?.expectedStarRating ?? baseline.energy,
      water: baseline.water + (result ? (result.expectedStarRating - baseline.energy) * 0.3 : 0),
      waste: baseline.waste + (result ? (result.expectedStarRating - baseline.energy) * 0.2 : 0),
      carbon: baseline.carbon + (result ? 0.5 : 0),
  };

  return (
    <div>
        <h3 className="text-xl font-bold mb-4">NABERS Performance Dashboard: <span className="text-day-accent dark:text-night-accent grey-theme:text-grey-accent">{params.buildingType}</span></h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <PerformanceItem icon="bolt" title="Energy" baseline={baseline.energy} target={target.energy} simulated={simulatedRatings.energy} isLoading={isLoading} color="text-yellow-400"/>
            <PerformanceItem icon="droplet" title="Water" baseline={baseline.water} target={target.water} simulated={simulatedRatings.water} isLoading={isLoading} color="text-blue-400" />
            <PerformanceItem icon="trash" title="Waste" baseline={baseline.waste} target={target.waste} simulated={simulatedRatings.waste} isLoading={isLoading} color="text-green-400"/>
            <IEQPerformanceItem result={result} isLoading={isLoading} onExplain={onExplainIEQ} />
            <PerformanceItem icon="leaf" title="Carbon" baseline={baseline.carbon} target={target.carbon} simulated={simulatedRatings.carbon} isLoading={isLoading} color="text-gray-400"/>
        </div>
    </div>
  );
};

export default PerformancePanel;
