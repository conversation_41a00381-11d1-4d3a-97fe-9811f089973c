import { retragreen<PERSON><PERSON> } from './logo';
import React from 'react';
import { AudienceMode, Theme } from '../types';
import { Icon } from './Icon';

interface HeaderProps {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  audienceMode: AudienceMode;
  setAudienceMode: (mode: AudienceMode) => void;
}

const Header: React.FC<HeaderProps> = ({ theme, setTheme, audienceMode, setAudienceMode }) => {
  const themes = [Theme.Day, Theme.Grey, Theme.Night];
  const audienceModes = [AudienceMode.Consultant, AudienceMode.Owner, AudienceMode.FM];
  
  const themeConfig = {
    [Theme.Day]: { icon: 'sun', text: 'Daylight', bg: 'bg-day-card', textClass: 'text-day-text', accent: 'bg-day-accent' },
    [Theme.Grey]: { icon: 'cloud', text: 'Grey', bg: 'bg-grey-card', textClass: 'text-grey-text', accent: 'bg-grey-accent' },
    [Theme.Night]: { icon: 'moon', text: 'Night', bg: 'bg-night-card', textClass: 'text-night-text', accent: 'bg-night-accent' },
  } as const;

  return (
    <header className={`p-4 shadow-md sticky top-0 z-10 transition-colors duration-300 ${themeConfig[theme].bg} ${themeConfig[theme].textClass}`}>
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <img src="https://static.wixstatic.com/media/4c27f2_9b0367b1cceb4b4bb227ff1732acfb0b~mv2.png/v1/crop/x_399,y_161,w_465,h_498/fill/w_84,h_90,al_c,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/IconOnly_Transparent.png" alt="Retragreen Logo" className="h-8 w-auto" />
          <h1 className="text-base sm:text-lg xl:text-xl font-bold hidden sm:block">
            NABERS Framework Scenario Simulation for AI HVAC Financial and Carbon Impact
          </h1>
        </div>
        <div className="flex items-center space-x-2 sm:space-x-4">
          <div className="hidden md:flex items-center space-x-2 p-1 rounded-full bg-gray-200 dark:bg-night-bg grey-theme:bg-grey-bg">
            {audienceModes.map((mode) => (
              <button
                key={mode}
                onClick={() => setAudienceMode(mode)}
                className={`px-3 py-1 text-sm font-medium rounded-full transition-colors ${
                  audienceMode === mode ? `${themeConfig[theme].accent} text-white` : 'hover:bg-gray-300 dark:hover:bg-gray-700'
                }`}
              >
                {mode}
              </button>
            ))}
          </div>
          <div className="flex items-center space-x-2 p-1 rounded-full bg-gray-200 dark:bg-night-bg grey-theme:bg-grey-bg">
            {themes.map((t) => (
              <button
                key={t}
                onClick={() => setTheme(t)}
                className={`p-2 rounded-full transition-colors ${
                  theme === t ? `${themeConfig[theme].accent}` : 'hover:bg-gray-300 dark:hover:bg-gray-700'
                }`}
                aria-label={`Switch to ${t} mode`}
              >
                <Icon name={themeConfig[t].icon} className={`h-5 w-5 ${
                    theme === t ? 'text-white' : 'text-gray-500 dark:text-gray-300 grey-theme:text-gray-300'
                }`} />
              </button>
            ))}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;