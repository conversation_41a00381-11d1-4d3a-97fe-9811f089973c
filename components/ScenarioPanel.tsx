import React from 'react';
import { Icon } from './Icon';
import { type Scenario } from '../types';

interface ScenarioPanelProps {
    scenarios: Scenario[];
    onSaveScenario: (name: string) => void;
    onDeleteScenario: (index: number) => void;
}

const ScenarioPanel: React.FC<ScenarioPanelProps> = ({ scenarios, onSaveScenario, onDeleteScenario }) => {
  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  const themeButtonClass = "bg-day-accent hover:bg-day-accent-hover dark:bg-night-accent dark:hover:bg-night-accent-hover grey-theme:bg-grey-accent grey-theme:hover:bg-grey-accent-hover";
  
  const handleSaveClick = () => {
    const name = prompt("Enter a name for this scenario:", "<PERSON> Scenario");
    if (name) {
        onSaveScenario(name);
    }
  };

  return (
    <div className={`p-6 rounded-lg shadow-lg border ${themeCardClass} ${themeBorderClass}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold flex items-center">
            <Icon name="clipboard" className="h-6 w-6 mr-2" />
            Scenario Builder
        </h3>
        <button 
            onClick={handleSaveClick}
            className={`px-4 py-2 text-sm text-white font-semibold rounded-lg shadow-sm transition-transform transform hover:scale-105 ${themeButtonClass}`}
        >
            Save Current
        </button>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left">
          <thead className="text-xs uppercase bg-day-bg dark:bg-night-bg grey-theme:bg-grey-bg">
            <tr>
              <th scope="col" className="px-4 py-3">Scenario Name</th>
              <th scope="col" className="px-4 py-3">Expected Stars</th>
              <th scope="col" className="px-4 py-3">Cost Savings</th>
              <th scope="col" className="px-4 py-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            {scenarios.length > 0 ? scenarios.map((scenario, index) => (
              <tr key={index} className={`border-b ${themeBorderClass}`}>
                <td className="px-4 py-3 font-medium text-day-accent dark:text-night-accent grey-theme:text-grey-accent">{scenario.name}</td>
                <td className="px-4 py-3">{scenario.result.expectedStarRating.toFixed(1)}</td>
                <td className="px-4 py-3">${scenario.financialImpact.costSavings.toLocaleString('en-US', { maximumFractionDigits: 0 })}</td>
                <td className="px-4 py-3 flex items-center space-x-2">
                    <button onClick={() => onDeleteScenario(index)} className="p-1 text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full" aria-label="Delete scenario">
                        <Icon name="trash" className="h-4 w-4"/>
                    </button>
                </td>
              </tr>
            )) : (
                <tr className={`border-b ${themeBorderClass}`}>
                    <td colSpan={4} className="text-center py-4 text-gray-500 dark:text-gray-400">
                        No scenarios saved yet.
                    </td>
                </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ScenarioPanel;