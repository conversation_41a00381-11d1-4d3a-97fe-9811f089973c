import React, { useState } from 'react';
import { AudienceMode } from '../types';
import { Icon } from './Icon';
import { knowledgebase } from '../knowledgebase';

interface ExplanationPanelProps {
  audienceMode: AudienceMode;
}

const ExplanationPanel: React.FC<ExplanationPanelProps> = ({ audienceMode }) => {
  const [isOpen, setIsOpen] = useState(false);

  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  const accentColor = "text-day-accent dark:text-night-accent grey-theme:text-grey-accent";
  
  const explanations = knowledgebase.roles;

  const currentExplanation = explanations[audienceMode];

  return (
    <div className={`fixed bottom-0 left-0 right-0 p-2 sm:p-4 z-20 transition-all duration-300`}>
        <div className={`max-w-4xl mx-auto rounded-lg shadow-2xl border ${themeCardClass} ${themeBorderClass} overflow-hidden`}>
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full p-3 sm:p-4 flex justify-between items-center text-left hover:bg-black/5 dark:hover:bg-white/5"
                aria-expanded={isOpen}
                aria-controls="explanation-content"
            >
                <h2 className="text-base sm:text-lg font-bold flex items-center">
                    <Icon name="info" className={`h-6 w-6 mr-3 flex-shrink-0 ${accentColor}`} />
                    <span className="hidden sm:inline">How to use this simulation: </span>
                    <span className="font-normal sm:ml-1">{currentExplanation.title}</span>
                </h2>
                <Icon name={isOpen ? 'chevron-down' : 'chevron-up'} className={`h-6 w-6 transition-transform transform ${isOpen ? 'rotate-0' : '-rotate-180'}`} />
            </button>
            <div
                id="explanation-content"
                className={`transition-all duration-300 ease-in-out grid ${isOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}
             >
                <div className="overflow-hidden">
                    <div className="p-3 sm:p-4 pt-0 text-sm sm:text-base">
                        <p className="opacity-90">{currentExplanation.content}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
  );
};

export default ExplanationPanel;