
import React, { useState } from 'react';
import { BuildingType, type SimulationParams, type SimulationResult } from '../types';
import { BASELINE_NABERS, TARGET_NABERS } from '../constants';
import { Icon } from './Icon';

interface OntologyViewProps {
    params: SimulationParams;
    result: SimulationResult | null;
    isLoading: boolean;
}

const OntologyView: React.FC<OntologyViewProps> = ({ params, result, isLoading }) => {
    const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

    const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
    const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
    const accentColor = "text-day-accent dark:text-night-accent grey-theme:text-grey-accent";
    
    const categories = [
        { name: 'Energy', icon: 'bolt' },
        { name: 'Water', icon: 'droplet' },
        { name: 'Waste', icon: 'trash' },
        { name: 'IEQ', icon: 'air' },
        { name: 'Carbon', icon: 'leaf' },
    ] as const;

    const baseline = BASELINE_NABERS[params.buildingType];
    const target = TARGET_NABERS[params.buildingType];

    const simulatedRatings = {
      energy: result?.expectedStarRating ?? baseline.energy,
      water: baseline.water + (result ? (result.expectedStarRating - baseline.energy) * 0.3 : 0),
      waste: baseline.waste + (result ? (result.expectedStarRating - baseline.energy) * 0.2 : 0),
      ieq: baseline.ieq + (result ? (result.expectedStarRating - baseline.energy) * 0.4 : 0),
      carbon: baseline.carbon + (result ? 0.5 : 0),
    };

    const getRatingsForCategory = (categoryName: string) => {
        switch (categoryName.toLowerCase()) {
            case 'energy': return { base: baseline.energy, targ: target.energy, sim: simulatedRatings.energy };
            case 'water': return { base: baseline.water, targ: target.water, sim: simulatedRatings.water };
            case 'waste': return { base: baseline.waste, targ: target.waste, sim: simulatedRatings.waste };
            case 'ieq': return { base: baseline.ieq, targ: target.ieq, sim: simulatedRatings.ieq };
            case 'carbon': return { base: baseline.carbon, targ: target.carbon, sim: simulatedRatings.carbon };
            default: return { base: 0, targ: 0, sim: 0 };
        }
    }

    return (
        <div className={`p-6 rounded-lg shadow-lg border relative ${themeCardClass} ${themeBorderClass}`}>
            <h3 className="text-lg font-bold mb-4 flex items-center">
                <Icon name="share" className="h-6 w-6 mr-2" />
                Ontology View
            </h3>
            <div 
                className="flex items-center justify-center space-x-4 sm:space-x-8"
                onMouseLeave={() => setHoveredCategory(null)}
            >
                <div className="text-center">
                    <Icon name="building" className={`h-12 w-12 sm:h-16 sm:w-16 mx-auto ${accentColor}`} />
                    <p className="font-bold mt-2 text-sm sm:text-base">{params.buildingType}</p>
                </div>
                <div className="text-3xl sm:text-5xl font-thin text-gray-400">&rarr;</div>
                <div className="grid grid-cols-3 gap-2 sm:gap-4">
                    {categories.map(cat => (
                        <div 
                            key={cat.name} 
                            className="flex flex-col items-center p-2 rounded-lg bg-day-bg dark:bg-night-bg grey-theme:bg-grey-bg group transition-transform hover:scale-110 cursor-pointer"
                            onMouseEnter={() => setHoveredCategory(cat.name)}
                        >
                            <Icon name={cat.icon} className="h-6 w-6 sm:h-8 sm:w-8 text-gray-500 group-hover:text-day-accent dark:group-hover:text-night-accent" />
                            <p className="text-xs mt-1">{cat.name}</p>
                        </div>
                    ))}
                </div>
            </div>
            
            {hoveredCategory && !isLoading && (
                <div className={`absolute bottom-full mb-2 left-1/2 -translate-x-1/2 w-48 p-2 rounded-lg shadow-xl border text-xs animate-fadeIn ${themeCardClass} ${themeBorderClass}`}>
                    <h5 className="font-bold text-center mb-1">{hoveredCategory} Ratings</h5>
                    <div className="flex justify-between"><span>Baseline:</span> <span>{getRatingsForCategory(hoveredCategory).base.toFixed(1)}</span></div>
                    <div className="flex justify-between"><span>Target:</span> <span>{getRatingsForCategory(hoveredCategory).targ.toFixed(1)}</span></div>
                    <div className={`flex justify-between font-bold ${accentColor}`}><span>Simulated:</span> <span>{getRatingsForCategory(hoveredCategory).sim.toFixed(1)}</span></div>
                </div>
            )}
        </div>
    );
};

export default OntologyView;