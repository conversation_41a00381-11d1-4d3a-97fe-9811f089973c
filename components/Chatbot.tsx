
import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { Icon } from './Icon';
import { type ChatMessage, ChatbotMode } from '../types';

interface ChatbotProps {
  isOpen: boolean;
  onToggle: () => void;
  history: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  chatbotMode: ChatbotMode;
  setChatbotMode: (mode: ChatbotMode) => void;
}

const Chatbot: React.FC<ChatbotProps> = ({ isOpen, onToggle, history, onSendMessage, isLoading, chatbotMode, setChatbotMode }) => {
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const themeAccentClass = "bg-day-accent text-white dark:bg-night-accent grey-theme:bg-grey-accent";
  const themeCardClass = "bg-day-card text-day-text dark:bg-night-card dark:text-night-text grey-theme:bg-grey-card grey-theme:text-grey-text";
  const themeBorderClass = "border-day-border dark:border-night-border grey-theme:border-grey-border";
  const themeInputClass = "bg-day-bg dark:bg-night-bg grey-theme:bg-grey-bg";

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [history, isLoading]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      onSendMessage(input.trim());
      setInput('');
    }
  };

  return (
    <>
      <div className={`fixed bottom-6 right-6 z-30 transition-all duration-300 ${isOpen ? 'opacity-0 scale-90' : 'opacity-100 scale-100'}`}>
        <button
          onClick={onToggle}
          className={`p-4 rounded-full shadow-lg transition-transform transform hover:scale-110 ${themeAccentClass}`}
          aria-label="Open chatbot"
        >
          <Icon name="chat-bubble" className="h-8 w-8" />
        </button>
      </div>

      <div className={`fixed bottom-6 right-6 z-40 w-[calc(100%-3rem)] sm:w-96 h-[70vh] max-h-[600px] transition-all duration-300 origin-bottom-right ${isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'}`}>
        <div className={`w-full h-full rounded-xl shadow-2xl border flex flex-col ${themeCardClass} ${themeBorderClass}`}>
          {/* Header */}
          <div className={`p-4 flex justify-between items-center border-b ${themeBorderClass}`}>
            <h3 className="font-bold text-lg flex items-center">
              <Icon name="robot" className="h-6 w-6 mr-2" />
              Retragreen AI Assistant
            </h3>
            <button onClick={onToggle} className="p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10" aria-label="Close chatbot">
              <Icon name="close" className="h-6 w-6" />
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-4">
              {history.map((msg, index) => (
                <div key={index} className={`flex items-start gap-3 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                  {msg.role === 'model' && <Icon name="robot" className={`h-6 w-6 flex-shrink-0 rounded-full p-1 ${themeAccentClass}`} />}
                  <div className={`max-w-[80%] p-3 rounded-xl ${msg.role === 'user' ? `rounded-br-none ${themeAccentClass}` : `rounded-bl-none ${themeInputClass}`}`}>
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                        <ReactMarkdown>{msg.parts[0].text}</ReactMarkdown>
                    </div>
                  </div>
                </div>
              ))}
              {isLoading && (
                 <div className="flex items-start gap-3">
                    <Icon name="robot" className={`h-6 w-6 flex-shrink-0 rounded-full p-1 ${themeAccentClass}`} />
                    <div className={`max-w-[80%] p-3 rounded-xl rounded-bl-none ${themeInputClass}`}>
                      <div className="flex items-center space-x-1">
                          <span className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                          <span className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                          <span className="h-2 w-2 bg-current rounded-full animate-bounce"></span>
                      </div>
                    </div>
                 </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Input Area */}
          <div className={`p-3 border-t ${themeBorderClass}`}>
            {/* Chat Mode Buttons */}
            <div className="mb-3">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-1.5 px-1">Chat Mode:</div>
              <div className="flex items-center gap-2">
                {Object.values(ChatbotMode).map(mode => (
                  <button
                    key={mode}
                    onClick={() => setChatbotMode(mode)}
                    className={`flex-1 text-sm py-1.5 px-2 rounded-lg transition-colors flex items-center gap-2 justify-center font-semibold ${
                      chatbotMode === mode
                        ? `${themeAccentClass}`
                        : `hover:bg-black/10 dark:hover:bg-white/10 ${themeInputClass}`
                    }`}
                  >
                    <Icon name={mode === ChatbotMode.Explanation ? 'info' : 'settings'} className="h-4 w-4" />
                    <span>{mode}</span>
                  </button>
                ))}
              </div>
            </div>
            
            {/* Input Form */}
            <form onSubmit={handleSubmit} className="flex items-center gap-2">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask a question..."
                className={`flex-1 p-2 rounded-lg border focus:ring-2 focus:outline-none ${themeInputClass} ${themeBorderClass}`}
                disabled={isLoading}
              />
              <button
                type="submit"
                disabled={isLoading || !input.trim()}
                className={`p-3 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${themeAccentClass}`}
              >
                <Icon name="send" className="h-5 w-5" />
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Chatbot;