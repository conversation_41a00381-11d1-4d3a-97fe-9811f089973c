
import React from 'react';
import { type SimulationParams, type SimulationR<PERSON>ult, type FinancialImpact, AudienceMode, type Scenario } from '../types';
import PerformancePanel from './PerformancePanel';
import MonteCarloChart from './MonteCarloChart';
import FinancialPanel from './FinancialPanel';
import OntologyView from './OntologyView';
import ScenarioPanel from './ScenarioPanel';
import { Icon } from './Icon';

interface DashboardProps {
  params: SimulationParams;
  result: SimulationResult | null;
  financialImpact: FinancialImpact | null;
  isLoading: boolean;
  audienceMode: AudienceMode;
  scenarios: Scenario[];
  onSaveScenario: (name: string) => void;
  onDeleteScenario: (index: number) => void;
  onExplainIEQ: () => void;
  onExplainMonteCarlo: () => void;
}

const LoadingSkeleton: React.FC = () => (
    <div className="w-full h-64 rounded-lg bg-day-card dark:bg-night-card grey-theme:bg-grey-card animate-pulse border border-day-border dark:border-night-border grey-theme:border-grey-border"></div>
);

const Dashboard: React.FC<DashboardProps> = ({ params, result, financialImpact, isLoading, audienceMode, scenarios, onSaveScenario, onDeleteScenario, onExplainIEQ, onExplainMonteCarlo }) => {
  return (
    <div className="space-y-6 animate-fadeIn">
      <PerformancePanel params={params} result={result} isLoading={isLoading} onExplainIEQ={onExplainIEQ} />
      
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <div className="xl:col-span-2">
            {isLoading || !result ? <LoadingSkeleton /> : <MonteCarloChart result={result} onExplain={onExplainMonteCarlo} />}
        </div>
        <div>
            {isLoading || !financialImpact ? <LoadingSkeleton /> : <FinancialPanel financialImpact={financialImpact} audienceMode={audienceMode} />}
        </div>
      </div>
       
      {(audienceMode === AudienceMode.Consultant || audienceMode === AudienceMode.FM) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <OntologyView params={params} result={result} isLoading={isLoading} />
          <ScenarioPanel 
            scenarios={scenarios}
            onSaveScenario={onSaveScenario}
            onDeleteScenario={onDeleteScenario}
            />
        </div>
      )}
    </div>
  );
};

export default Dashboard;
