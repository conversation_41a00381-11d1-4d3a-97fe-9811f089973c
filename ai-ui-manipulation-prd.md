# Product Requirements Document: AI UI Direct Manipulation

**Author:** <PERSON>tragreen AI Assistant
**Version:** 1.2
**Date:** October 28, 2023
**Status:** Proposed

---

## 1. Executive Summary

This document outlines the requirements for the "AI UI Direct Manipulation" feature for the NABERS Framework Simulation Web App. This feature will empower users to modify and control the simulation's input parameters directly through the chatbot interface using natural language. This enhancement builds directly upon the recently refactored, mode-aware chatbot architecture.

The primary goal is to create a more fluid and powerful user experience by adding a conversational control layer on top of the existing, fully-functional simulation parameters. Instead of being a passive informational tool, the chatbot will become an active partner in the user's workflow, capable of understanding and executing commands to alter the simulation state.

## 2. Problem & User Value

**Problem:** The current workflow requires users to manually interact with sliders and dropdowns in the "Simulation Controls" panel. While effective, this can be cumbersome for power users who want to rapidly test different hypotheses or for users who prefer a conversational interface.

**User Value:**
- **Increased Efficiency:** Users can change multiple parameters or run simulations with a single text command, significantly speeding up scenario analysis.
- **Enhanced Usability & Accessibility:** Provides an alternative, conversational method of control that can be more intuitive and accessible for some users.
- **"Wow" Factor:** Demonstrates a sophisticated level of AI integration, elevating the application from a simple calculator to an intelligent, interactive tool.

## 3. User Stories

- **As a NABERS Consultant,** I want to be able to say, *"Set the building type to Hotel, change AI effectiveness to 90%, and run the simulation,"* so that I can quickly generate and compare scenarios for my clients without extensive manual clicking.
- **As a Building Owner,** I want to ask, *"What would the savings be for a 20,000 square meter building where the energy cost is 25 cents?"* so that I can immediately see the financial implications of different scenarios.
- **As a Facility Manager,** I want to command the bot, *"Tighten the temperature band to 22-23 degrees,"* to instantly see the impact on the IEQ score and the resulting energy overhead.

## 4. Functional Requirements

### 4.1. Intent Recognition and Parameter Extraction
- The AI must be able to parse user input in the "Set Model Parameters" mode to identify:
    1.  The **intent** to change one or more parameters.
    2.  The specific **parameter(s)** to be changed. The AI must map natural language requests (e.g., "AI effectiveness", "cost of power") to the existing `SimulationParams` state keys (e.g., `aiEffectiveness`, `energyCost`).
    3.  The target **value(s)** for those parameters (e.g., `Hotel`, `90`, `[22, 23]`).
    4.  The **intent** to trigger an action, specifically `runSimulation`.

### 4.2. Structured Data Response from AI
- The `chatbotService` must leverage the Gemini model's JSON mode.
- The service will request a structured response containing two key parts:
    1.  `text`: A human-readable confirmation message (e.g., "Okay, I've set the building type to Hotel.").
    2.  `actions`: An array of machine-readable action objects that the frontend can execute.
- **Example Action Object:** ` { "type": "SET_SIMULATION_PARAM", "payload": { "key": "buildingType", "value": "Hotel" } }`
- **Example Action Object:** ` { "type": "RUN_SIMULATION" }`

### 4.3. Frontend Action Execution
- The `App.tsx` component will receive the `actions` array from the `chatbotService`.
- It will process each action in the array, updating its state accordingly by calling `setSimulationParams`.
- If a `RUN_SIMULATION` action is present, it will trigger the `handleRunSimulation` function after all parameter changes have been applied.
- The UI controls (sliders, dropdowns) **must** visually update to reflect the new state set by the chatbot, providing clear visual feedback to the user.

### 4.4. Conversational Feedback and Disambiguation
- **Confirmation:** The chatbot must always confirm the action it is about to take before the UI updates (e.g., "Alright, setting AI effectiveness to 75%.").
- **Summarization (Post-Action):** After a simulation is run via a chat command, the bot should ideally provide a concise summary of the key results, e.g., "The new simulation is complete. Your expected star rating is now 5.4."
- **Disambiguation:** If a command is unclear (e.g., "Change the band"), the bot must ask for clarification ("Do you mean the temperature band or the humidity band?").
- **Error Handling:** If an invalid value is provided (e.g., "Set fresh air to 100"), the bot must respond with a helpful error message that includes the valid range ("Fresh air intake must be between 5 and 20 L/s/person.").

## 5. Technical Implementation Plan

**Prerequisites:** This plan assumes the target parameters are already implemented as part of the React state (`SimulationParams`) and are controllable via the UI. This work focuses exclusively on adding the AI conversational control layer.

1.  **Update `types.ts`:**
    - Define a `UIAction` interface:
      ```typescript
      export interface UIAction {
        type: 'SET_SIMULATION_PARAM' | 'RUN_SIMULATION';
        payload?: {
          key: keyof SimulationParams;
          value: any;
        };
      }
      ```

2.  **Update `services/promptService.ts`:**
    - For `ChatbotMode.ParameterSetting`, heavily revise the system instruction.
    - Instruct the model that it **MUST** respond in JSON format according to a provided schema.
    - The prompt should clearly define the available parameters, their keys (`aiEffectiveness`, etc.), and valid value types.
    - Remove the old rule about telling the user the feature is under development.

3.  **Update `services/chatbotService.ts`:**
    - Modify `getBotResponse` to return `Promise<{ text: string; actions: UIAction[] }>`.
    - Configure the `ai.chats.create` call to use JSON mode:
      ```typescript
      const chat = ai.chats.create({
          model: 'gemini-2.5-flash',
          config: {
              systemInstruction,
              responseMimeType: "application/json",
              responseSchema: {
                  type: Type.OBJECT,
                  properties: {
                      text: { type: Type.STRING },
                      actions: {
                          type: Type.ARRAY,
                          items: { /* Schema for UIAction */ }
                      }
                  }
              }
          },
          //...
      });
      ```
    - Parse the JSON response from `response.text` and return the structured object.

4.  **Update `App.tsx`:**
    - Modify `handleSendMessage` to await the new structured response from `getBotResponse`.
    - Create a new handler function, `executeUIActions(actions: UIAction[])`.
    - This handler will loop through the actions. For `SET_SIMULATION_PARAM`, it will update a temporary params object. After the loop, it will call `setSimulationParams` once with the accumulated changes. If a `RUN_SIMULATION` action exists, it will then call `handleRunSimulation`.
    - The bot's text response will be added to the chat history as usual.

## 6. Scope

### In-Scope (v1.0)
- The ability to set all parameters currently present in the `ControlPanel` component, including `buildingType`, `aiEffectiveness`, `occupantDensity`, `temperatureBand`, `humidityBand`, `freshAirIntake`, `buildingArea`, `energyCost`, and `hvacEnergyShare`.
- The ability to trigger a simulation run.
- Single-intent commands (e.g., "Set X to Y").
- Chained-intent commands (e.g., "Set X to Y and A to B").
- Basic error handling for out-of-range values.

### Out-of-Scope (Future Considerations)
- Manipulating saved scenarios (saving, deleting, loading).
- Changing application-level state like Theme or Audience Mode.
- Complex, multi-turn conversational flows for setting up a scenario.
- Proactive suggestions from the AI (e.g., "You could improve your IEQ score by increasing fresh air. Would you like to try?").

---
**End of Document**