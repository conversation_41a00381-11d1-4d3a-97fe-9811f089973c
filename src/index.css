@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles to enable 'grey-theme' variant */
.grey-theme .grey-theme\:bg-grey-bg { background-color: #4b5563; }
.grey-theme .grey-theme\:text-grey-text { color: #f9fafb; }
.grey-theme .grey-theme\:bg-grey-card { background-color: #374151; }
.grey-theme .grey-theme\:border-grey-border { border-color: #6b7280; }
.grey-theme .grey-theme\:accent-grey-accent { accent-color: #60a5fa; }
.grey-theme .grey-theme\:hover\:bg-grey-accent-hover:hover { background-color: #3b82f6; }
.grey-theme .grey-theme\:bg-grey-accent { background-color: #60a5fa; }
.grey-theme .grey-theme\:text-grey-accent { color: #60a5fa; }
.grey-theme .grey-theme\:text-gray-300 { color: #d1d5db; }

/* Fix for recharts tooltip theming */
.recharts-default-tooltip {
    background-color: #fffffff0 !important;
    border-color: #e5e7eb !important;
    color: #1f2937 !important;
    border-radius: 0.5rem;
}
.dark .recharts-default-tooltip, .grey-theme .recharts-default-tooltip {
    background-color: #1f2937f0 !important;
    border-color: #374151 !important;
    color: #d1d5db !important;
}
.recharts-tooltip-item-name, .recharts-tooltip-item-separator, .recharts-tooltip-item-value {
  color: #1f2937 !important;
}
.dark .recharts-tooltip-item-name, .dark .recharts-tooltip-item-separator, .dark .recharts-tooltip-item-value,
.grey-theme .recharts-tooltip-item-name, .grey-theme .recharts-tooltip-item-separator, .grey-theme .recharts-tooltip-item-value {
  color: #d1d5db !important;
}

/* Animation classes */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
}