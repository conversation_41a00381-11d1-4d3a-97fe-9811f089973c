# GitHub-as-Database Storage Upgrade

## Product Requirements Document (PRD)
**Version:** 1.0  
**Date:** January 2025  
**Project:** NABERS Simulation Framework Storage Enhancement

## 1. Executive Summary

### 1.1 Overview
Implement a lightweight storage solution using GitHub repositories as the database backend for the NABERS simulation framework, enabling multi-user scenario persistence.

### 1.2 Business Objectives
- Zero Cost: No expensive database solutions
- Portability: Easy rehosting without database migration
- Multi-User Support: Concurrent scenario management
- Simplicity: Maintain lightweight architecture

## 2. Solution Architecture

### 2.1 GitHub-as-Database Design
```
nabers-scenarios/ (GitHub Repository)
├── users/
│   ├── [username].json
│   └── temp_session_[id].json
├── shared/
│   └── public_scenarios.json
└── locks/
    └── scenariolocks.json
```

### 2.2 Technology Stack
- Frontend: React + TypeScript
- API Layer: Netlify Functions
- Storage: GitHub Repository (private)
- Authentication: Username-based identification

## 3. Feature Requirements

### 3.1 User Identification
- Simple username input (no password)
- localStorage persistence
- Username validation (3-50 alphanumeric chars)
- Anonymous user support
- URL-based user switching

### 3.2 Scenario Persistence
- Save/load JSON scenarios (~5KB each)
- Data validation and sanitization
- Export/import functionality
- Automatic conflict resolution

### 3.3 Concurrency Management
- File-based locking
- Automatic retry with backoff
- Conflict detection
- 30-minute lock expiration

## 4. API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/scenarios/save` | POST | Save user scenarios |
| `/api/scenarios/load` | GET | Load user scenarios |
| `/api/scenarios/lock` | POST | Acquire scenario lock |
| `/api/scenarios/lock` | DELETE | Release scenario lock |

## 5. Implementation Plan

### 5.1 Phase 1: Core (Week 1)
- GitHub Repository Setup
- Netlify Functions
- User Identification

### 5.2 Phase 2: Scenario Management (Week 2)
- Frontend Integration
- Data Migration

### 5.3 Phase 3: Advanced Features (Week 3)
- Concurrency Control
- Performance Optimization

### 5.4 Phase 4: Testing (Week 4)
- Unit/Integration Tests
- Documentation

## 6. Success Criteria

### 6.1 Functional
- User sign-in works
- Scenarios persist across sessions
- Concurrent saves work
- Conflict resolution functions

### 6.2 Performance
- Save: <3s
- Load: <2s
- Supports 10+ concurrent users

## 7. Future Enhancements
- Team workspaces
- Version history
- Scenario templates
- Analytics

**Total Effort:** 4 weeks  
**Launch Target:** February 2025
