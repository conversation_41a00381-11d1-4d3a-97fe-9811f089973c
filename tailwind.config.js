/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'day-bg': '#f3f4f6',
        'day-text': '#1f2937',
        'day-card': '#ffffff',
        'day-border': '#e5e7eb',
        'day-accent': '#2563eb',
        'day-accent-hover': '#1d4ed8',

        'grey-bg': '#4b5563',
        'grey-text': '#f9fafb',
        'grey-card': '#374151',
        'grey-border': '#6b7280',
        'grey-accent': '#60a5fa',
        'grey-accent-hover': '#3b82f6',

        'night-bg': '#111827',
        'night-text': '#d1d5db',
        'night-card': '#1f2937',
        'night-border': '#374151',
        'night-accent': '#3b82f6',
        'night-accent-hover': '#2563eb',
      }
    }
  },
  plugins: [],
}