import { type SimulationParams, type SimulationResult, type FinancialImpact, OccupantDensity, PaybackStatus } from '../types';
import { BASELINE_NABERS, TARGET_NABERS, STARS_TO_ENERGY_REDUCTION, CARBON_INTENSITY_FACTOR, ENERGY_STAR_THRESHOLD, BASELINE_EUI, AI_SYSTEM_CAPEX_RANGE, AI_SYSTEM_OPEX_RANGE } from '../constants';

// A simple implementation of the Box-Muller transform to generate normally distributed random numbers.
const randomNormal = (mean = 0, stdDev = 1): number => {
    let u1 = 0, u2 = 0;
    while (u1 === 0) u1 = Math.random(); //Converting [0,1) to (0,1)
    while (u2 === 0) u2 = Math.random();
    const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
    return z0 * stdDev + mean;
};

/**
 * Simulates the energy overhead and quality score for Indoor Environment Quality (IEQ).
 */
const simulateIEQ = (params: SimulationParams) => {
    const { aiEffectiveness, occupantDensity, temperatureBand, humidityBand, freshAirIntake } = params;

    // 1. Calculate Energy Overhead based on IEQ parameters
    const densityMultiplier = { [OccupantDensity.Low]: 0.7, [OccupantDensity.Typical]: 1.0, [OccupantDensity.High]: 1.4 };
    
    // Tighter bands require more energy. Base overhead is a fraction of total EUI.
    const tempDiff = temperatureBand[1] - temperatureBand[0];
    const tempOverhead = (1 / Math.max(0.5, tempDiff)) * 5; // kWh/m^2/year penalty for tight bands

    const humidityDiff = humidityBand[1] - humidityBand[0];
    const humidityOverhead = (1 / Math.max(2, humidityDiff)) * 3;

    // Ventilation energy is based on intake rate and density
    const ventilationOverhead = freshAirIntake * densityMultiplier[occupantDensity] * 1.5;

    const totalIeqEnergyOverhead = tempOverhead + humidityOverhead + ventilationOverhead;

    // 2. Calculate IEQ Score
    // Start with a base score and adjust based on AI and parameters
    let ieqScore = 85 + (aiEffectiveness - 80) * 0.2; // Base score of 85 for 80% effective AI
    if (occupantDensity === OccupantDensity.High) ieqScore -= 5; // Harder to maintain with more people
    if (tempDiff < 1.5) ieqScore -= 3; // Tight bands are harder to maintain
    if (freshAirIntake < 8) ieqScore -= 5; // Low fresh air impacts quality
    ieqScore = Math.max(60, Math.min(98, ieqScore)); // Clamp score between 60 and 98

    // 3. Calculate Complaint Reduction
    // Simple linear model: score of 65 = 0% reduction, score of 95 = 90% reduction
    const complaintReduction = Math.max(0, (ieqScore - 65) / 30 * 90);

    return {
        totalIeqEnergyOverhead,
        ieqScore,
        complaintReduction,
    };
};


export const runMonteCarlo = (params: SimulationParams): SimulationResult => {
    const { buildingType, aiEffectiveness, iterations, hvacEnergyShare } = params;

    const baseEnergyStars = BASELINE_NABERS[buildingType].energy;
    const targetEnergyStars = TARGET_NABERS[buildingType].energy;

    const neededStars = Math.round(targetEnergyStars - baseEnergyStars);
    const requiredReduction = STARS_TO_ENERGY_REDUCTION[neededStars] || 0;

    // New, more realistic logic: The mean reduction is based on what the AI can save 
    // from the HVAC portion of the building's total energy consumption.
    const hvacEnergyPortion = BASELINE_EUI[buildingType] * (hvacEnergyShare / 100);
    const meanReduction = hvacEnergyPortion * (aiEffectiveness / 100);
    const stdDev = meanReduction * 0.15; // Standard deviation is a percentage of the mean saving

    const results: number[] = [];
    for (let i = 0; i < iterations; i++) {
        results.push(Math.max(0, randomNormal(meanReduction, stdDev)));
    }

    // Run IEQ micro-simulation
    const { totalIeqEnergyOverhead, ieqScore, complaintReduction } = simulateIEQ(params);
    const ieqEnergyOverheadPercent = (totalIeqEnergyOverhead / BASELINE_EUI[buildingType]) * 100;

    // Adjust energy savings based on IEQ overhead
    const grossAvgEnergySaving = results.reduce((sum, val) => sum + val, 0) / results.length;
    const finalAvgEnergySaving = Math.max(0, grossAvgEnergySaving - totalIeqEnergyOverhead);

    const successfulIterations = results.filter(r => (r - totalIeqEnergyOverhead) >= requiredReduction).length;
    const probabilityMeetingTarget = successfulIterations / iterations;

    const nabersImprovement = finalAvgEnergySaving / ENERGY_STAR_THRESHOLD;
    const expectedStarRating = Math.min(6, baseEnergyStars + nabersImprovement);

    return {
        energySavingsDistribution: results,
        probabilityMeetingTarget,
        expectedStarRating,
        avgEnergySaving: finalAvgEnergySaving,
        // IEQ results
        ieqScore,
        ieqEnergyOverheadPercent,
        complaintReduction,
    };
};

/**
 * Validates CAPEX and OPEX values are within reasonable ranges
 */
const validateFinancialParams = (aiSystemCapex: number, aiSystemOpex: number): { isValid: boolean; warnings: string[] } => {
    const warnings: string[] = [];
    let isValid = true;

    // Validate CAPEX range
    if (aiSystemCapex < AI_SYSTEM_CAPEX_RANGE.MIN) {
        warnings.push(`CAPEX below minimum recommended ($${AI_SYSTEM_CAPEX_RANGE.MIN.toLocaleString()})`);
        isValid = false;
    } else if (aiSystemCapex > AI_SYSTEM_CAPEX_RANGE.MAX) {
        warnings.push(`CAPEX above maximum recommended ($${AI_SYSTEM_CAPEX_RANGE.MAX.toLocaleString()})`);
        isValid = false;
    }

    // Validate OPEX range
    if (aiSystemOpex < AI_SYSTEM_OPEX_RANGE.MIN) {
        warnings.push(`OPEX below minimum expected ($${AI_SYSTEM_OPEX_RANGE.MIN.toLocaleString()}/year)`);
    } else if (aiSystemOpex > AI_SYSTEM_OPEX_RANGE.MAX) {
        warnings.push(`OPEX above maximum expected ($${AI_SYSTEM_OPEX_RANGE.MAX.toLocaleString()}/year)`);
    }

    return { isValid, warnings };
};

export const calculateFinancialImpact = (avgEnergySaving: number, params: SimulationParams): FinancialImpact => {
    const { buildingArea, energyCost, aiSystemCapex, aiSystemOpex } = params;
    
    // Validate financial parameters
    const validation = validateFinancialParams(aiSystemCapex, aiSystemOpex);
    if (!validation.isValid) {
        console.warn('Financial parameter validation warnings:', validation.warnings);
    }
    
    // Calculate annual savings (existing logic)
    const costSavings = avgEnergySaving * energyCost * buildingArea;
    const carbonSavings = (avgEnergySaving * CARBON_INTENSITY_FACTOR * buildingArea) / 1000; // in tCO2-e

    // Calculate dynamic payback period
    const netAnnualSavings = costSavings - aiSystemOpex;
    let paybackPeriod: number | null = null;
    let paybackStatus: PaybackStatus;
    
    if (netAnnualSavings <= 0) {
        // OPEX exceeds or equals annual savings - never pays back
        paybackStatus = PaybackStatus.Unprofitable;
    } else {
        paybackPeriod = aiSystemCapex / netAnnualSavings;
        
        if (paybackPeriod < 10) {
            paybackStatus = PaybackStatus.Profitable;
        } else if (paybackPeriod <= 25) {
            paybackStatus = PaybackStatus.LongTerm;
        } else {
            // Unrealistic payback period - treat as unprofitable
            paybackStatus = PaybackStatus.Unprofitable;
            paybackPeriod = null;
        }
    }

    return {
        costSavings,
        carbonSavings,
        paybackPeriod,
        paybackStatus,
    };
};