
// FIX: The 'Chat' type is part of the '@google/genai' library and should be imported from there, not from local types.
import { type Chat } from '@google/genai';
import { type ChatMessage, AudienceMode, type SimulationParams, type SimulationResult, ChatbotMode } from '../types';
import { ai } from './geminiClient';
import { createSystemInstruction } from './promptService';

/**
 * This service acts as the primary interface for interacting with the chatbot.
 * It encapsulates the logic for creating prompts, managing chat sessions (in a stateless manner for now),
 * and communicating with the Gemini API.
 *
 * This structure is designed for future extension, such as adding intent recognition
 * to allow the chatbot to modify the UI.
 */

/**
 * Sends the user's message and chat history to the Gemini model and gets a response.
 *
 * @param history The full conversation history.
 * @param audienceMode The current role of the user, which determines the bot's persona.
 * @param params The current simulation parameters from the UI.
 * @param result The current simulation result from the UI.
 * @param chatbotMode The chatbot's current operational mode (e.g., Explanation).
 * @returns A promise that resolves to the bot's text response.
 */
export const getBotResponse = async (
    history: ChatMessage[], 
    audienceMode: AudienceMode,
    params: SimulationParams | null,
    result: SimulationResult | null,
    chatbotMode: ChatbotMode
): Promise<string> => {
    // 1. Create a role-specific and data-aware system instruction.
    const systemInstruction = createSystemInstruction(audienceMode, params, result, chatbotMode);
    
    // 2. We create a new, stateless chat session for each request. This ensures the system
    // instruction is always up-to-date with the latest audience mode and live data, avoiding
    // complexities with managing long-lived, stateful chat objects.
    const chat: Chat = ai.chats.create({
        model: 'gemini-2.5-flash',
        config: {
            systemInstruction,
        },
        // The history sent to create() seeds the conversation. We exclude the latest
        // user message because we will send it via sendMessage().
        history: history.slice(0, -1), 
    });
    
    // 3. Send the most recent user message to get the next response.
    const lastMessage = history[history.length - 1];
    const response = await chat.sendMessage({ message: lastMessage.parts[0].text });

    // 4. Return the text part of the response.
    // In the future, this function could return an object like { text: string, actions: UIAction[] }
    // to allow the bot to interact with the UI.
    return response.text;
};