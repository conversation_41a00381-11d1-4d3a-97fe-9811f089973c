import { AudienceMode, type SimulationParams, type Simulation<PERSON><PERSON>ult, ChatbotMode } from '../types';
import { knowledgebase } from '../knowledgebase';

/**
 * Constructs a detailed, role-specific system instruction for the Gemini model.
 * This function tailors the bot's persona and knowledge based on the selected audience mode
 * and injects live simulation data if available.
 *
 * @param audienceMode The current user role (e.g., Consultant, Owner, FM).
 * @param params The current simulation parameters.
 * @param result The current simulation result.
 * @param chatbotMode The chatbot's current operational mode (Explanation or Parameter Setting).
 * @returns A string containing the full system instruction for the AI.
 */
export const createSystemInstruction = (
    audienceMode: AudienceMode,
    params: SimulationParams | null,
    result: SimulationResult | null,
    chatbotMode: ChatbotMode
): string => {
    const roleInfo = knowledgebase.roles[audienceMode];
    const modeInfo = knowledgebase.chatbotModes[chatbotMode];

    const promptSections = [
        "You are a specialist AI assistant for the Retragreen NABERS Simulation App. Your primary goal is to provide helpful, accurate, and role-specific advice to users.",
        
        `**CURRENT CHAT MODE: ${modeInfo.title}**\n- **Your Goal:** ${modeInfo.instruction}\n- **Mode-Specific Rules:**\n${modeInfo.rules.map(r => `  * ${r}`).join('\n')}`,
        
        "**GENERAL RULES:**\n1.  **FORMATTING:** Use simple markdown for formatting (like **bolding** or *italics*) and bullet points (using '*') for lists. NEVER use HTML tags.\n2.  **BE CONCISE:** Keep your answers short and to the point. Use lists to break up information. The user is in a small chat window.",
        
        `**BASE KNOWLEDGE:**\n- NABERS Context: ${knowledgebase.context}\n- Retragreen AI System: ${knowledgebase.aiSystemContext}\n- Simulation Method: ${knowledgebase.simulationMethodology.content}\n- Financial Model: ${knowledgebase.simulationCalculations.content}`,

        // Conditionally add specialized knowledge based on mode
        chatbotMode === ChatbotMode.Explanation ? knowledgebase.chatbotModes[ChatbotMode.Explanation].specializedKnowledge : '',
        chatbotMode === ChatbotMode.ParameterSetting ? knowledgebase.chatbotModes[ChatbotMode.ParameterSetting].settableParameters : '',

        `**YOUR CURRENT USER PERSONA:**\nYou MUST adapt your tone, focus, and examples based on the user's currently selected role in the app. The current user's role is: **${audienceMode}**.`,
        `- **Your Focus:** ${roleInfo.focus}\n- **Key Talking Points:** ${roleInfo.keyUses.join(', ')}\n- **Value Proposition to Emphasize:** ${roleInfo.valueProposition}`,
    ];

    // Dynamically add live data context if available
    if (result && params) {
        const liveDataContext = [
            "**LIVE SIMULATION DATA CONTEXT (Use this for specific answers):**",
            `- Current Building Type: ${params.buildingType}`,
            `- Building Area: ${params.buildingArea.toLocaleString()} m²`,
            `- Energy Cost: $${params.energyCost.toFixed(2)}/kWh`,
            `- HVAC Energy Share: ${params.hvacEnergyShare}%`,
            `- AI System CAPEX: $${(params.aiSystemCapex / 1000).toFixed(0)}k`,
            `- AI System OPEX: $${(params.aiSystemOpex / 1000).toFixed(0)}k/year`,
            `- IEQ Score: ${result.ieqScore.toFixed(0)}/100`,
            `- IEQ Energy Overhead: ${result.ieqEnergyOverheadPercent.toFixed(1)}%`,
            `- Complaint Reduction: ${result.complaintReduction.toFixed(0)}%`,
            `- Monte Carlo Probability of Target: ${(result.probabilityMeetingTarget * 100).toFixed(1)}%`,
            `- Average Energy Saving: ${result.avgEnergySaving.toFixed(1)} kWh/m²/year`,
            `- Monte Carlo Iterations: ${result.energySavingsDistribution.length.toLocaleString()}`,
            `- AI Control Effectiveness: ${params.aiEffectiveness}%`,
            `\n**CRITICAL INSTRUCTION FOR LIVE DATA:** When the user asks about IEQ or the Monte Carlo chart (e.g., from a prompt like 'Explain my current IEQ results'), you **MUST** integrate the specific numbers from the 'LIVE SIMULATION DATA CONTEXT' into your answer. For example, instead of a general explanation, say 'Your current IEQ Score is **${result.ieqScore.toFixed(0)}/100**, which is contributing **${result.ieqEnergyOverheadPercent.toFixed(1)}%** to your energy overhead.' For Monte Carlo explanations, always use the actual probability (${(result.probabilityMeetingTarget * 100).toFixed(1)}%), average saving (${result.avgEnergySaving.toFixed(1)} kWh/m²/year), and iteration count (${result.energySavingsDistribution.length.toLocaleString()}) from the live data. NEVER use hardcoded values like "10,000 simulation runs" - always use the actual iteration count. This makes your response directly relevant to what the user is seeing.`
        ].join('\n');
        promptSections.push(liveDataContext);
    }

    promptSections.push("Always be helpful. Do not make up information or provide financial advice. If you don't know the answer, say that you can only answer questions related to the NABERS simulation and Retragreen's AI system.");

    return promptSections.filter(Boolean).join('\n\n');
};