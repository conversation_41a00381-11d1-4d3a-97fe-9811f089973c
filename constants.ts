
import { BuildingType, type NabersRatings } from './types';

export const BUILDING_TYPES: BuildingType[] = [
  BuildingType.Office,
  BuildingType.Hotel,
  BuildingType.ShoppingCentre,
  BuildingType.Hospital,
];

// Baseline Energy Use Intensity (kWh/m²/year) for HVAC systems
export const BASELINE_EUI: Record<BuildingType, number> = {
    [BuildingType.Office]: 220,
    [BuildingType.Hotel]: 350,
    [BuildingType.ShoppingCentre]: 400,
    [BuildingType.Hospital]: 450,
};

export const BASELINE_NABERS: Record<BuildingType, NabersRatings> = {
  [BuildingType.Office]: { energy: 3.0, water: 2.5, waste: 3.0, ieq: 3.5, carbon: 0 },
  [BuildingType.Hotel]: { energy: 2.5, water: 2.0, waste: 2.5, ieq: 3.0, carbon: 0 },
  [BuildingType.ShoppingCentre]: { energy: 2.0, water: 1.8, waste: 2.2, ieq: 3.2, carbon: 0 },
  [BuildingType.Hospital]: { energy: 2.5, water: 2.2, waste: 2.8, ieq: 3.5, carbon: 0 },
};

export const TARGET_NABERS: Record<BuildingType, NabersRatings> = Object.fromEntries(
  Object.entries(BASELINE_NABERS).map(([bldg, ratings]) => [
    bldg,
    {
      energy: Math.min(6, ratings.energy + 2),
      water: Math.min(6, ratings.water + 2),
      waste: Math.min(6, ratings.waste + 2),
      ieq: Math.min(6, ratings.ieq + 2),
      carbon: 1, // Target for Carbon is always 1 (Carbon Neutral)
    },
  ])
) as Record<BuildingType, NabersRatings>;

export const STARS_TO_ENERGY_REDUCTION: Record<number, number> = {
  1: 25,
  2: 50,
  3: 75,
  4: 100,
  5: 125,
  6: 150,
};

export const ENERGY_COST_PER_KWH = 0.18; // AUD
export const CARBON_INTENSITY_FACTOR = 0.85; // kg CO2/kWh
export const ENERGY_STAR_THRESHOLD = 25; // kWh/m^2/year per NABERS star

// Financial validation constants
export const AI_SYSTEM_CAPEX_RANGE = {
  MIN: 50000,  // $50k minimum
  MAX: 500000, // $500k maximum
  DEFAULT: 150000 // $150k default
};

export const AI_SYSTEM_OPEX_RANGE = {
  MIN: 5000,   // $5k/year minimum
  MAX: 50000,  // $50k/year maximum
  DEFAULT: 15000 // $15k/year default
};