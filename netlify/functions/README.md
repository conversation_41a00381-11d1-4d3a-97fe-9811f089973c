# Netlify Functions

This directory is for Netlify serverless functions. Currently empty, but ready for future API endpoints.

## Usage

Create `.js` or `.ts` files in this directory to deploy as serverless functions.

Example function structure:
```typescript
// netlify/functions/hello.ts
export const handler = async (event, context) => {
  return {
    statusCode: 200,
    body: JSON.stringify({
      message: "Hello from Netlify Functions!"
    })
  }
}
```

Access at: `https://your-site.netlify.app/.netlify/functions/hello`

## Future Use Cases

- API proxy for secure Gemini API calls
- Data processing endpoints
- Authentication handlers
- File upload processing

## Documentation

- [Netlify Functions Docs](https://docs.netlify.com/functions/overview/)
- [TypeScript Support](https://docs.netlify.com/functions/build-with-typescript/)