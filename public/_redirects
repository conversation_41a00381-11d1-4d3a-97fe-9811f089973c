# Netlify redirects file for SPA (Single Page Application) routing
# This ensures all routes are handled by the client-side React router

# Redirect all routes to index.html with 200 status (SPA fallback)
/*    /index.html   200

# Optional: API proxying (if you add API endpoints later)
# /api/*  https://your-api-domain.com/api/:splat  200

# Optional: Redirect old URLs (add as needed)
# /old-route  /new-route  301