import React, { useState, useEffect, useCallback } from 'react';
import { BuildingType, AudienceMode, Theme, type SimulationParams, type SimulationResult, type FinancialImpact, type Scenario, OccupantDensity, type ChatMessage, ChatbotMode } from './types';
import { BUILDING_TYPES, AI_SYSTEM_CAPEX_RANGE, AI_SYSTEM_OPEX_RANGE } from './constants';
import { runMonteCarlo, calculateFinancialImpact } from './services/simulationService';
import { getBotResponse } from './services/chatbotService';
import Header from './components/Header';
import ControlPanel from './components/ControlPanel';
import Dashboard from './components/Dashboard';
import ExplanationPanel from './components/ExplanationPanel';
import Chatbot from './components/Chatbot';

const App: React.FC = () => {
  const [theme, setTheme] = useState<Theme>(Theme.Night);
  const [audienceMode, setAudienceMode] = useState<AudienceMode>(AudienceMode.Consultant);
  const [simulationParams, setSimulationParams] = useState<SimulationParams>({
    buildingType: BuildingType.Office,
    aiEffectiveness: 80,
    iterations: 5000,
    buildingArea: 10000,
    energyCost: 0.18,
    hvacEnergyShare: 45,
    aiSystemCapex: AI_SYSTEM_CAPEX_RANGE.DEFAULT,
    aiSystemOpex: AI_SYSTEM_OPEX_RANGE.DEFAULT,
    occupantDensity: OccupantDensity.Typical,
    temperatureBand: [21.5, 23.5],
    humidityBand: [45, 55],
    freshAirIntake: 10,
  });
  const [simulationResult, setSimulationResult] = useState<SimulationResult | null>(null);
  const [financialImpact, setFinancialImpact] = useState<FinancialImpact | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [scenarios, setScenarios] = useState<Scenario[]>([]);
  
  // Chatbot state
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [isBotLoading, setIsBotLoading] = useState(false);
  const [chatbotMode, setChatbotMode] = useState<ChatbotMode>(ChatbotMode.Explanation);

  // Load scenarios from localStorage on initial render
  useEffect(() => {
    try {
      const savedScenarios = localStorage.getItem('nabers-scenarios');
      if (savedScenarios) {
        setScenarios(JSON.parse(savedScenarios));
      }
    } catch (error) {
      console.error("Failed to load scenarios from localStorage", error);
      localStorage.removeItem('nabers-scenarios');
    }
  }, []);

  // Save scenarios to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem('nabers-scenarios', JSON.stringify(scenarios));
    } catch (error) {
      console.error("Failed to save scenarios to localStorage", error);
    }
  }, [scenarios]);

  useEffect(() => {
    document.documentElement.classList.remove('dark', 'grey-theme');
    if (theme === Theme.Night) {
      document.documentElement.classList.add('dark');
    } else if (theme === Theme.Grey) {
      document.documentElement.classList.add('grey-theme');
    }
  }, [theme]);

  const handleRunSimulation = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => {
      const result = runMonteCarlo(simulationParams);
      setSimulationResult(result);
      if (result) {
        const financialResult = calculateFinancialImpact(result.avgEnergySaving, simulationParams);
        setFinancialImpact(financialResult);
      }
      setIsLoading(false);
    }, 1000);
  }, [simulationParams]);

  useEffect(() => {
    handleRunSimulation();
     // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSaveScenario = (name: string) => {
    if (name && simulationResult && financialImpact) {
      const newScenario: Scenario = {
        name,
        params: { ...simulationParams },
        result: { ...simulationResult },
        financialImpact: { ...financialImpact },
      };
      setScenarios(prev => [...prev, newScenario]);
    }
  };

  const handleDeleteScenario = (index: number) => {
    setScenarios(prev => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async (message: string) => {
      const userMessage: ChatMessage = { role: 'user', parts: [{ text: message }] };
      const newHistory = [...chatHistory, userMessage];
      setChatHistory(newHistory);
      setIsBotLoading(true);

      try {
          const botResponse = await getBotResponse(newHistory, audienceMode, simulationParams, simulationResult, chatbotMode);
          const botMessage: ChatMessage = { role: 'model', parts: [{ text: botResponse }] };
          setChatHistory([...newHistory, botMessage]);
      } catch (error) {
          console.error("Gemini API error:", error);
          const errorMessage: ChatMessage = { role: 'model', parts: [{ text: "Sorry, I'm having trouble connecting. Please try again later." }] };
          setChatHistory([...newHistory, errorMessage]);
      } finally {
          setIsBotLoading(false);
      }
  };
  
  const handleExplainIEQ = () => {
    setIsChatbotOpen(true);
    setChatbotMode(ChatbotMode.Explanation);
    if (isBotLoading) return;
    handleSendMessage("Explain my current IEQ results in detail, using the live data.");
  };
  
  const handleExplainMonteCarlo = () => {
    setIsChatbotOpen(true);
    setChatbotMode(ChatbotMode.Explanation);
    if(isBotLoading) return;
    handleSendMessage("Explain this Monte Carlo chart using my current results.");
  };

  const themeClasses = {
    [Theme.Day]: 'bg-day-bg text-day-text',
    [Theme.Grey]: 'bg-grey-bg text-grey-text',
    [Theme.Night]: 'bg-night-bg text-night-text',
  };

  return (
    <div className={`min-h-screen font-sans transition-colors duration-300 ${themeClasses[theme]}`}>
      <Header
        theme={theme}
        setTheme={setTheme}
        audienceMode={audienceMode}
        setAudienceMode={setAudienceMode}
      />
      <main className="p-4 sm:p-6 lg:p-8 mb-32">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          <div className="lg:col-span-3">
            <ControlPanel
              params={simulationParams}
              setParams={setSimulationParams}
              onRunSimulation={handleRunSimulation}
              isLoading={isLoading}
              buildingTypes={BUILDING_TYPES}
            />
          </div>
          <div className="lg:col-span-9">
            <Dashboard
              params={simulationParams}
              result={simulationResult}
              financialImpact={financialImpact}
              isLoading={isLoading}
              audienceMode={audienceMode}
              scenarios={scenarios}
              onSaveScenario={handleSaveScenario}
              onDeleteScenario={handleDeleteScenario}
              onExplainIEQ={handleExplainIEQ}
              onExplainMonteCarlo={handleExplainMonteCarlo}
            />
          </div>
        </div>
      </main>
      <ExplanationPanel audienceMode={audienceMode} />
      <Chatbot
        isOpen={isChatbotOpen}
        onToggle={() => setIsChatbotOpen(!isChatbotOpen)}
        history={chatHistory}
        onSendMessage={handleSendMessage}
        isLoading={isBotLoading}
        chatbotMode={chatbotMode}
        setChatbotMode={setChatbotMode}
      />
    </div>
  );
};

export default App;