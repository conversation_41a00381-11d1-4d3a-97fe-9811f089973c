<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NABERS Framework Simulation</title>
    <script type="importmap">
    {
      "imports": {
        "react": "https://aistudiocdn.com/react@^19.1.1",
        "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
        "react/": "https://aistudiocdn.com/react@^19.1.1/",
        "recharts": "https://aistudiocdn.com/recharts@^3.1.2",
        "@google/genai": "https://aistudiocdn.com/@google/genai@^1.16.0",
        "react-markdown": "https://aistudiocdn.com/react-markdown@^10.1.0"
      }
    }
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              'day-bg': '#f3f4f6',
              'day-text': '#1f2937',
              'day-card': '#ffffff',
              'day-border': '#e5e7eb',
              'day-accent': '#2563eb',
              'day-accent-hover': '#1d4ed8',

              'grey-bg': '#4b5563',
              'grey-text': '#f9fafb',
              'grey-card': '#374151',
              'grey-border': '#6b7280',
              'grey-accent': '#60a5fa',
              'grey-accent-hover': '#3b82f6',

              'night-bg': '#111827',
              'night-text': '#d1d5db',
              'night-card': '#1f2937',
              'night-border': '#374151',
              'night-accent': '#3b82f6',
              'night-accent-hover': '#2563eb',
            }
          }
        }
      }
    </script>
    <style>
      /* Custom styles to enable 'grey-theme' variant */
      .grey-theme .grey-theme\:bg-grey-bg { background-color: #4b5563; }
      .grey-theme .grey-theme\:text-grey-text { color: #f9fafb; }
      .grey-theme .grey-theme\:bg-grey-card { background-color: #374151; }
      .grey-theme .grey-theme\:border-grey-border { border-color: #6b7280; }
      .grey-theme .grey-theme\:accent-grey-accent { accent-color: #60a5fa; }
      .grey-theme .grey-theme\:hover\:bg-grey-accent-hover:hover { background-color: #3b82f6; }
      .grey-theme .grey-theme\:bg-grey-accent { background-color: #60a5fa; }
      .grey-theme .grey-theme\:text-grey-accent { color: #60a5fa; }
      .grey-theme .grey-theme\:text-gray-300 { color: #d1d5db; }

      /* Fix for recharts tooltip theming */
      .recharts-default-tooltip {
          background-color: #fffffff0 !important;
          border-color: #e5e7eb !important;
          color: #1f2937 !important;
          border-radius: 0.5rem;
      }
      .dark .recharts-default-tooltip, .grey-theme .recharts-default-tooltip {
          background-color: #1f2937f0 !important;
          border-color: #374151 !important;
          color: #d1d5db !important;
      }
      .recharts-tooltip-item-name, .recharts-tooltip-item-separator, .recharts-tooltip-item-value {
        color: #1f2937 !important;
      }
      .dark .recharts-tooltip-item-name, .dark .recharts-tooltip-item-separator, .dark .recharts-tooltip-item-value,
      .grey-theme .recharts-tooltip-item-name, .grey-theme .recharts-tooltip-item-separator, .grey-theme .recharts-tooltip-item-value {
        color: #d1d5db !important;
      }

      /* Animation classes */
      @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
      }
      .animate-fadeIn {
          animation: fadeIn 0.5s ease-out forwards;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
