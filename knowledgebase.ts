// knowledgebase.ts
import { AudienceMode, ChatbotMode } from './types';

export const knowledgebase = {
  context:
    'NABERS (National Australian Built Environment Rating System) is a performance-based rating system that assesses the operational efficiency of buildings across categories such as Energy, Water, Waste, Indoor Environment (IEQ), and Carbon Neutrality. High NABERS ratings increase a building’s value, attract tenants, lower operational costs, and are essential for ESG compliance.',
  
  aiSystemContext: 
    'Retragreen’s AI HVAC Control System directly supports the acceleration and maintenance of high NABERS ratings by dynamically optimizing energy and environmental performance, identifying system inefficiencies, and supporting predictive maintenance strategies.',

  coreCapabilities: [
    { name: 'Energy Optimization', details: 'Reduces HVAC energy use by 15–30% via real-time dynamic control, predictive load balancing, and whole-system modeling.', impact: 'Directly impacts NABERS Energy Rating.' },
    { name: 'Water Optimization', details: 'Monitors and optimizes cooling tower water use, alerts for leaks, and identifies inefficient heat transfer.', impact: 'Directly impacts NABERS Water Rating.' },
    { name: 'IEQ & Comfort Management', details: 'Integrates sensor data (CO₂, RH, Temp, VOCs) and occupant feedback to guide control decisions without compromising comfort.', impact: 'Directly supports NABERS Indoor Environment Rating.' },
    { name: 'Carbon Neutrality', details: 'Quantifies emissions based on energy profiles and reports savings in tCO₂-e avoided.', impact: 'Supports NABERS Carbon Neutral rating.' },
    { name: 'Waste Management Support', details: 'Tracks equipment runtime and asset utilization to optimize replacement cycles and minimize unnecessary operation.', impact: 'Indirectly supports NABERS Waste Rating.' },
  ],
  
  aiTechniques: [
      { name: 'Clustering & Classification', use: 'Identifies overcooling, zoning inefficiencies, and failing equipment.' },
      { name: 'Deep Learning', use: 'Models nonlinear relationships across HVAC, weather, occupancy, and grid data.' },
      { name: 'Reinforcement Learning', use: 'Continuously learns and improves setpoints based on feedback loops.' },
      { name: 'Time Series Forecasting', use: 'Anticipates demand and adjusts proactively.' },
      { name: 'Generative AI', use: 'Produces human-readable alerts and reports for different user roles.' },
  ],

  simulationMethodology: {
    title: "Monte Carlo Simulation Implementation",
    content: "The Monte Carlo simulation is a genuine, statistically valid process that runs entirely client-side in the user's browser using TypeScript/JavaScript. It does not use or connect to a Python backend. The methodology involves repeated random sampling using a standard statistical method (the Box-Muller transform) to model the uncertainty in AI performance. While the language is different from what might be used in a data science environment (like Python with NumPy), the mathematical principles and the results are identical, providing a legitimate simulation with the benefit of instant, interactive feedback."
  },

  monteCarloMethodology: {
    title: "Advanced Monte Carlo Simulation Context & Application",
    content: "Our Monte Carlo implementation operates as a **probabilistic forecasting engine** within the broader NABERS rating simulation framework. It uses the mathematically rigorous **Box-Muller transform** to generate normally distributed random samples, modeling the inherent uncertainty in AI-driven HVAC energy optimization performance.\n\n**Core Architecture:**\n• **Baseline Foundation**: Uses building-specific EUI (Energy Use Intensity) in kWh/m²/year as the fundamental metric\n• **HVAC-Focused Application**: Applies AI effectiveness only to the HVAC portion (typically 40-60% of total building energy consumption)\n• **Realistic Performance Modeling**: 15% standard deviation captures real-world performance fluctuations due to weather, occupancy patterns, and equipment variations\n• **Mathematical Constraints**: Non-negative constraint ensures no negative energy savings scenarios\n\n**Statistical Rigor & Implementation:**\n• **Client-Side Processing**: Runs entirely in-browser using TypeScript without requiring backend computational resources\n• **Genuine Randomness**: Uses cryptographically sound random number generation for statistical validity\n• **Mathematical Equivalence**: While implemented in JavaScript rather than Python/R, the Box-Muller transform produces identical statistical properties to traditional data science implementations\n\n**Simulation Objectives:**\n1. **Risk Assessment**: Models probability of achieving specific NABERS energy rating improvements (e.g., 2-star increase)\n2. **Performance Uncertainty Modeling**: Captures reality that AI systems don't perform identically across all operational conditions\n3. **Investment Decision Support**: Provides probability distributions rather than point estimates, enabling informed financial decisions with confidence intervals\n4. **Regulatory Compliance**: Generates statistically valid evidence for NABERS assessments, moving beyond simple percentage estimates to probabilistic forecasting\n\n**Professional Value Proposition:**\nUnlike deterministic models providing single-point estimates, our Monte Carlo approach acknowledges that AI performance inherently varies and provides stakeholders with confidence intervals for ROI calculations, risk-adjusted project evaluation, probabilistic compliance forecasting, and evidence-based NABERS rating projections. This positions the simulation as a professional-grade decision support tool with the statistical rigor expected in energy consulting and building performance optimization."
  },

  simulationCalculations: {
    title: "Simulation Financial & Carbon Calculation Details",
    content: "The financial and carbon impact is derived from several key inputs. Each **Building Type** has a **Baseline Energy Use Intensity (EUI)** (kWh/m²/year). A key realistic constraint is the **HVAC Energy Share (%)**, which defines what portion of the building's total energy is used by the HVAC system (typically 40-60%). The AI's energy savings are calculated against this HVAC portion only, based on its effectiveness. This per-square-meter saving is then scaled by the **Building Area (m²)** to get the total annual kWh saved. This is multiplied by the **Energy Cost ($/kWh)** for the final financial saving.\n\n**Dynamic Payback Period Calculation**: The investment payback period is calculated using the formula: **Payback Period (years) = AI System CAPEX ÷ (Annual Cost Savings - AI System OPEX)**. The **AI System CAPEX** represents the one-time capital expenditure for hardware, installation, and setup (typically $50k-$500k). The **AI System OPEX** represents ongoing annual operational costs including software licenses, support, and maintenance (typically $5k-$50k/year). When annual savings exceed OPEX, the system provides positive ROI. When OPEX exceeds savings, the investment never pays back. The simulation provides three status categories: Profitable (< 10 years), Long-term (10-25 years), and Unprofitable (never pays back)."
  },

  roles: {
    [AudienceMode.Consultant]: {
      title: "For NABERS Consultants",
      content: "This simulation tool empowers you to build compelling business cases for your clients using professional-grade probabilistic forecasting. Our Monte Carlo implementation provides statistically rigorous evidence through the Box-Muller transform methodology, generating confidence intervals and risk-adjusted projections rather than simple point estimates. Use the controls to model various scenarios, showcasing the direct impact of AI-driven HVAC optimization—proven to reduce energy use by 15-30%—on NABERS ratings. The probabilistic approach provides the statistical rigor expected in energy consulting, offering evidence-based rating projections and regulatory compliance forecasting to accelerate certification timelines.",
      focus: "Statistical rigor, evidence-based projections, regulatory compliance, certification acceleration.",
      keyUses: [
        "Generating probabilistic NABERS rating improvement forecasts with confidence intervals.",
        "Providing statistically valid evidence for NABERS assessments and compliance documentation.",
        "Risk-adjusted scenario modeling with Monte Carlo projections for professional reporting.",
        "Evidence-based decision support moving beyond deterministic 'we expect X%' to 'Y% confidence of achieving Z target'.",
      ],
      valueProposition: "Delivers professional-grade statistical analysis for NABERS certification with probabilistic forecasting that meets the rigor expected in energy consulting and building performance optimization."
    },
    [AudienceMode.Owner]: {
      title: "For Building Owners & Asset Managers",
      content: "Unlock the financial potential of your asset with sophisticated investment decision support. Our probabilistic Monte Carlo simulation provides confidence intervals for ROI calculations rather than single-point estimates, enabling risk-adjusted project evaluation. By modeling the inherent uncertainty in AI-driven HVAC performance, you can instantly see projected annual cost reductions with statistical confidence levels and carbon abatement projections. An AI-enhanced building commands higher tenant satisfaction, lower operational expenditure, and a stronger green premium in the market. Use this probabilistic data to justify capital expenditure with professional-grade financial analysis and chart a course to a more profitable, ESG-compliant portfolio.",
      focus: "Risk-adjusted ROI, probabilistic financial analysis, asset value optimization, ESG compliance.",
      keyUses: [
        "Probabilistic energy and carbon cost savings analysis with confidence intervals.",
        "Risk-adjusted investment evaluation using Monte Carlo forecasting.",
        "Linking NABERS improvement probability to increased leasing potential and asset valuation.",
        "Statistical evidence for ESG dashboards and emissions reporting compliance.",
      ],
      valueProposition: "Delivers statistically rigorous ROI analysis with confidence intervals, supports evidence-based carbon neutrality claims, and enhances competitive positioning through professional-grade financial decision support."
    },
    [AudienceMode.FM]: {
      title: "For HVAC Facility Managers",
      content: "Gain predictive control over your building's performance with professional-grade uncertainty modeling. Our Monte Carlo simulation helps you understand the operational benefits and performance variability of an AI-powered HVAC strategy, providing realistic expectations rather than overly optimistic projections. The probabilistic approach models how AI optimization works 24/7 to reduce energy consumption while maintaining optimal indoor environmental quality (IEQ), accounting for real-world factors like weather variations, occupancy patterns, and equipment performance fluctuations. This moves you from reactive maintenance to a proactive, data-driven approach by providing statistical confidence in Fault Detection and Diagnostics (FDD) effectiveness for issues like stuck dampers, fouled filters, and sensor drift.",
      focus: "Probabilistic operations planning, uncertainty modeling, predictive maintenance, statistical performance expectations.",
      keyUses: [
        "Understanding performance variability and setting realistic operational expectations.",
        "Statistical confidence in AI-assisted FDD for proactive maintenance planning.",
        "Probabilistic energy reduction forecasting for budget planning and performance monitoring.",
        "Risk-adjusted operational decision making with confidence intervals.",
      ],
       valueProposition: "Enables transition from reactive firefighting to statistically-informed, proactive operational strategies with realistic performance expectations and uncertainty quantification for professional facility management."
    },
  },
  
  chatbotModes: {
      [ChatbotMode.Explanation]: {
        title: "Explanation Mode",
        instruction: "In this mode, your primary goal is to explain concepts, interpret the simulation results, and answer questions about *why* things are happening. You are a data analyst and teacher.",
        rules: [
          "Use the 'LIVE SIMULATION DATA CONTEXT' to make your explanations specific and relevant.",
          "When a user asks to change a parameter (e.g., 'set AI effectiveness to 90%'), you MUST guide them to the correct mode. Respond with something like: 'I can help with that in parameter setting mode. Please click the **'Set Model Parameters'** button, and then repeat your request.'",
          "Proactively guide the user back to the UI controls and charts to explore concepts.",
        ],
        specializedKnowledge: `**SPECIALIZED KNOWLEDGE: MONTE CARLO CHART EXPLANATION**\nWhen a user asks to explain the Monte Carlo chart, its probability, or why the target seems low, you MUST use the LIVE SIMULATION DATA CONTEXT values and this core explanation:\n"The chart's 'Probability of reaching target' can seem confusing. The key is that the simulation's goal is NOT the 'Avg Saving' line. The actual target is the energy saving required for a 2-star NABERS improvement (e.g., 50 kWh/m²/year for an Office Block). The probability shown is the percentage of simulation runs that successfully exceed this high target. The bell curve shows all possible outcomes from your current simulation. To increase this probability, try increasing the **'AI Control Effectiveness'** in the controls to shift the entire curve to the right."\n\n**CRITICAL: Always use the exact values from LIVE SIMULATION DATA CONTEXT:**\n• Use the actual "Monte Carlo Probability of Target" percentage\n• Use the actual "Average Energy Saving" value\n• Use the actual "Monte Carlo Iterations" count (NOT a hardcoded number like 10,000)\n• Reference the specific building type and parameters from the live data\n\n**MONTE CARLO X-AXIS METRIC EXPLANATION**\nWhen users ask about the X-axis units or what the energy savings values represent, confirm that **kWh/m²/year is the correct metric**. Technical explanation:\n• **Data Source**: The energySavingsDistribution contains values generated from the runMonteCarlo function\n• **Calculation Logic**: The simulation calculates meanReduction from hvacEnergyPortion × (aiEffectiveness / 100) where hvacEnergyPortion is BASELINE_EUI[buildingType] × (hvacEnergyShare / 100)\n• **Units**: BASELINE_EUI is defined as \"Baseline Energy Use Intensity (kWh/m²/year)\" in the constants\n• **Final Values**: The avgEnergySaving and distribution values represent energy savings in kWh/m²/year, which is the standard industry metric for building energy performance"`
      },
      [ChatbotMode.ParameterSetting]: {
        title: "Parameter Setting Mode",
        instruction: "In this mode, your primary goal is to understand user requests to change the simulation's input parameters. You are a helpful assistant, ready to configure the model.",
        rules: [
          "When a user asks for an explanation (e.g., 'why is the IEQ score low?'), you MUST guide them to the correct mode. Respond with something like: 'That's a great question. To give you a detailed answer, please switch to the **'Model Explanation'** mode and ask me again.'",
          "When you understand a request to change a parameter, CONFIRM the change clearly. For example, 'Okay, setting AI Control Effectiveness to 90%.'",
          "**IMPORTANT LIMITATION:** After confirming the change, you MUST inform the user that this feature is still under development and that for now, they need to use the UI controls on the left to apply the change. For example: 'This functionality is being integrated. For now, please use the slider in the Simulation Controls panel to set the AI effectiveness.'",
          "Do NOT attempt to explain the impact of the change. Keep your response focused on confirming the parameter update."
        ],
        settableParameters: `**SETTABLE PARAMETERS:**\n- **Building Type**: Can be set to 'Office Block', 'Hotel', 'Shopping Centre', or 'Hospital'.\n- **AI Control Effectiveness**: A percentage from 10 to 100.\n- **Building Area**: A number, e.g., '15000 m2'.\n- **Energy Cost**: A monetary value, e.g., '25 cents per kWh' or '$0.25'.\n- **HVAC Energy Share**: A percentage from 20 to 80, e.g., '45%'.\n- **AI System CAPEX**: One-time capital expenditure from $50k to $500k, e.g., '$150000' or '$150k'.\n- **AI System OPEX**: Annual operational expenditure from $5k to $50k per year, e.g., '$15000/year' or '$15k per year'.\n- **Occupant Density**: Can be 'Low', 'Typical', or 'High'.\n- **Desired Temperature Band**: Two numbers, e.g., '21 to 23 degrees'.\n- **Desired Humidity Band**: Two numbers, e.g., '45% to 55%'.\n- **Min Fresh Air Intake**: A number, e.g., '10 L/s/person'.`
      }
  }
};